---
apiVersion: v1
kind: Namespace
metadata:
  name: ${K8S_NAMESPACE} 
--- 
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${CI_PROJECT_NAME}
  namespace: ${K8S_NAMESPACE}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ${CI_PROJECT_NAME}
  template:
    metadata:
      labels:
        app: ${CI_PROJECT_NAME}
    spec:
      imagePullSecrets:
        - name: nexuscred
      hostAliases:
      - ip: "*************"
        hostnames:
        - "afonline-dev.post.kz"
      containers:
        - name: ${CI_PROJECT_NAME}-container
          image: ${IMAGE_FULL_NAME}:${BUILD_ID}
          env:
            - name: BUILD_ID
              value: ${IMAGE_FULL_NAME}:${BUILD_ID}
            - name: db_user
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: db_user
            - name: db_pass
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: db_pass
            - name: db_name
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: db_name
            - name: db_host
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: db_host
            - name: db_port
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: db_port
            - name: kafka_url
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: kafka_url
            - name: kafka_host
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: kafka_host
            - name: kafka_topic
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: kafka_topic
            - name: kafka_consumer_group
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: kafka_consumer_group
            - name: elastic_url
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: elastic_url
            - name: service_url
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: service_url
            - name: redis_host
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: redis_host
            - name: redis_port
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: redis_port
            - name: redis_pass
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: redis_pass
            - name: elastic_user
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: elastic_user
            - name: elastic_pass
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: elastic_pass
            - name: port
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: port
            - name: contour
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: contour
            - name: token_secret
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: token_secret
            - name: ldap_host
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: ldap_host
            - name: ldap_port
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: ldap_port
            - name: ldap_username
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: ldap_username
            - name: TZ
              value: "Asia/Oral" 
          ports:
            - containerPort: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: ${CI_PROJECT_NAME}-service
  namespace: ${K8S_NAMESPACE}
spec:
  selector:
    app: ${CI_PROJECT_NAME}
  ports:
    - protocol: TCP
      port: 443
      targetPort: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ${CI_PROJECT_NAME}-ingress
  namespace: ${K8S_NAMESPACE}
  annotations:
    kubernetes.io/ingress.class: nginx
spec:
  ingressClassName: nginx
  tls:
    - hosts:
      - afonline-dev.post.kz
      secretName: afonline-dev.post.kz-tls
  rules:
    - host: afonline-dev.post.kz
      http:
        paths:
          - path: /producer
            pathType: Prefix
            backend:
              service:
                name: ${CI_PROJECT_NAME}-service
                port:
                  number: 443