package models

type MoneyTransfers struct {
	ID                uint   `json:"id"`                 // tefo.id
	OperDateTime      string `json:"oper_date_time"`     // tefo.oper_date_time
	TransactionStatus int64  `json:"transaction_status"` // tefo.transaction_status
	SenderIIN         string `json:"sender_iin"`         // tp.iin
	RecipientIIN      string `json:"recipient_iin"`      // tp.iin
	AmountKzt         string `json:"amount_kzt"`         // tefod.amount_kzt
	SenderCardNumber  string `json:"sender_card_number"` // tefod.sender_card_number
	RecipCardNumber   string `json:"recip_card_number"`  // tefod.recip_card_number

	// tb_event_person tp,  tb_event_fin_operation tefo,  tb_event_fin_oper_details tefod
}
