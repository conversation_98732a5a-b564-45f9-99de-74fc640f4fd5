package models

import (
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/Shopify/sarama"
)

var (
	asyncProducer sarama.AsyncProducer
)

func NewKafka(env *Environment) (producer sarama.AsyncProducer, err error) {
	config := sarama.NewConfig()
	fmt.Println(env.ConnectionKafkaString)
	brokers := strings.Split(env.ConnectionKafkaString, ",")
	asyncProducer, err = sarama.NewAsyncProducer(brokers, config)
	if err != nil {
		return
	}
	producer = asyncProducer
	return
}

func GetAsyncProducer() sarama.AsyncProducer {
	return asyncProducer
}

func ProduceMessage(signals chan os.Signal, content string) {
	var topic = Env.KafkaTopic
	var producer = GetAsyncProducer()
	for {
		message := &sarama.ProducerMessage{Topic: topic, Value: sarama.StringEncoder(content)}
		select {
		case producer.Input() <- message:
			log.Println("New message produced to topic: ", topic)
			return

		case <-signals:
			log.Println("producer closed")
			producer.AsyncClose()
		}
	}
}
