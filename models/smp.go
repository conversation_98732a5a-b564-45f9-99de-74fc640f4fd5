package models

import (
	"github.com/google/uuid"
)

type SMP struct {
	EventID      uuid.UUID `json:"event_id"`       // event_id
	OperDateTime string    `json:"oper_date_time"` // tefo.oper_date_time
	ProductID    string    `json:"product_id"`     // tefo.product_id
	AmountKZT    float64   `json:"amount_kzt"`     // tefod.amount_kzt
	IIN          string    `json:"iin"`            // tp.iin
	FullName     string    `json:"full_name"`      // tp.full_name
	PersonType   int       `json:"person_type"`    // tp.person_type
	MobileNumber string    `json:"mobile_number"`  // tp.mobile_number
	BankName     string    `json:"bank_name"`      // tefod.bank_name
	LicAccount   string    `json:"lic_account"`    // tefod.lic_account

	// tb_event_person tp,  tb_event_fin_operation tefo,  tb_event_fin_oper_details tefod
}
