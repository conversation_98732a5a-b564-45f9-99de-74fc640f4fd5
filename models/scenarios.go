package models

type Scenario struct {
	Id       int    `gorm:"primaryKey" json:"id"`                                      //id сценария
	Name     string `gorm:"column:name;type:varchar;" json:"name"`                     //Название
	FuncName string `gorm:"-" json:"-"`                                                //Название функции (Системный атрибут)
	Enable   bool   `gorm:"column:is_enable;type:bool;" json:"is_enable"`              //Включен\выключен сценарий
	Desc     string `gorm:"column:description;type:varchar;" json:"description"`       //Описание
	DescKz   string `gorm:"column:description_kz;type:varchar;" json:"description_kz"` //Описание KZ
} //@name Scenario

func (Scenario) TableName() string {
	return "tb_scenarios"
}
