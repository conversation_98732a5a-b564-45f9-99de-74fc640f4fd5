package models

type Scoring struct {
	Id             int    `gorm:"primaryKey" json:"id"`                                     //id
	MinScore       int    `gorm:"column:min_score;type:int4;" json:"min_score"`             //Минимальный балл
	MaxScore       int    `gorm:"column:max_score;type:int4;" json:"max_score"`             //Максимальный балл
	RiskLevel      string `gorm:"column:risk_level;type:varchar;" json:"risk_level"`        //Уровень риска
	RiskLevelKz    string `gorm:"column:risk_level_kz;type:varchar;" json:"risk_level_kz"`  //Уровень риска KZ
	SystemId       int    `gorm:"column:system_id;type:int4;" json:"system_id"`             //Канал	(справочник каналов)
	EventTypeId    int    `gorm:"column:event_type_id;type:int4;" json:"event_type_id"`     //Тип события (спр.типов событий)
	DecisionStatus int    `gorm:"column:decision_status;type:int4;" json:"decision_status"` //Решение (справочник статусов)
} //@name Scoring

func (Scoring) TableName() string {
	return "tb_scoring_params"
}
