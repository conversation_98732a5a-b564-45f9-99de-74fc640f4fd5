package models

import (
	"fmt"
	"os"

	"github.com/joho/godotenv"
)

var Env Environment

type Environment struct {
	DbUser string `json:"db_user"`
	DbPass string `json:"db_pass"`
	DbName string `json:"db_name"`
	DbHost string `json:"db_host"`
	DbPort string `json:"db_port"`

	RedisHost string `json:"redis_host"`
	RedisDb   string `json:"redis_db"`
	RedisPass string `json:"redis_pass"`

	ServiceUrl string `json:"service_url"`
	Port       string `json:"port"`
}

func (e *Environment) Load() {
	var err error
	Env, err = NewEnvironment()
	if err != nil {
		panic(err)
	}

}

func NewEnvironment() (envs Environment, err error) {
	if os.Getenv("db_pass") == "" {
		err = godotenv.Load() //Load .env file
		if err != nil {
			return
		}
	}

	envs = Environment{
		DbUser: os.Getenv("db_user"),
		DbPass: os.Getenv("db_pass"),
		DbName: os.Getenv("db_name"),
		DbHost: os.Getenv("db_host"),
		DbPort: os.Getenv("db_port"),

		RedisHost: os.Getenv("redis_host"),
		RedisDb:   os.Getenv("redis_db"),
		RedisPass: os.Getenv("redis_pass"),

		ServiceUrl: os.Getenv("service_url"),
		Port:       os.Getenv("port"),
	}
	fmt.Println(envs)

	return
}
