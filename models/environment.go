package models

import (
	"fmt"
	"os"

	"github.com/joho/godotenv"
)

var Env Environment

type Environment struct {
	DbUser                  string `json:"db_user"`
	DbPass                  string `json:"db_pass"`
	DbName                  string `json:"db_name"`
	DbHost                  string `json:"db_host"`
	DbPort                  string `json:"db_port"`
	ConnectionKafkaString   string `json:"kafka_url"`
	KafkaTopic              string `json:"kafka_topic"`
	KafkaConsumerGroup      string `json:"kafka_consumer_group"`
	ConnectionElasticString string `json:"elastic_url"`
	ElasticUser             string `json:"elastic_user"`
	ElasticPass             string `json:"elastic_pass"`
	Port                    string `json:"port"`
}

func (e *Environment) Load() {
	var err error
	Env, err = NewEnvironment()
	if err != nil {
		panic(err)
	}

}

func NewEnvironment() (envs Environment, err error) {
	if os.Getenv("db_pass") == "" {
		err = godotenv.Load() //Load .env file
		if err != nil {
			return
		}
	}

	envs = Environment{
		DbUser:                  os.Getenv("db_user"),
		DbPass:                  os.Getenv("db_pass"),
		DbName:                  os.Getenv("db_name"),
		DbHost:                  os.Getenv("db_host"),
		DbPort:                  os.Getenv("db_port"),
		ConnectionKafkaString:   os.Getenv("kafka_url"),
		KafkaTopic:              os.Getenv("kafka_topic"),
		KafkaConsumerGroup:      os.Getenv("kafka_consumer_group"),
		ConnectionElasticString: os.Getenv("elastic_url"),
		Port:                    os.Getenv("port"),

		ElasticUser: os.Getenv("elastic_user"),
		ElasticPass: os.Getenv("elastic_pass"),
	}
	fmt.Println(envs)

	return
}
