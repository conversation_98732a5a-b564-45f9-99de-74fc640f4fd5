package models

type RelatedTransfers struct {
	OperDateTime      string `json:"oper_date_time"`     // efo.oper_date_time
	IIN               string `json:"iin"`                // tp.iin
	PersonDC          int64  `json:"person_dc"`          // ep.person_dc 1 Отпаравитель 2 получатель
	ProductID         string `json:"product_id"`         // efo.product_id
	AmountKzt         string `json:"amount_kzt"`         // efod.amount_kzt
	CardNumber        string `json:"card_number"`        // efod.card_number
	TransactionStatus int64  `json:"transaction_status"` // efo.transaction_status
	// tb_event_person ep,  tb_event_fin_operation efo,  tb_event_fin_oper_details efod
}

type ResponseJSONData struct {
	IIN string `json:"iin"`
}
