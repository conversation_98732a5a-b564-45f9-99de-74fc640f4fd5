package models

type Product struct {
	Code   string `gorm:"column:code;type:varchar;" json:"code"`       //код продукта
	NameEn string `gorm:"column:name_en;type:varchar;" json:"name_en"` //Наименование EN
	NameKk string `gorm:"column:name_kk;type:varchar;" json:"name_kz"` //Наименование KZ
	NameRu string `gorm:"column:name_ru;type:varchar;" json:"name_ru"` //Наименование RU
} //@name Product

func (Product) TableName() string {
	return "tb_fin_products"
}

type FinService struct {
	Code        string `gorm:"column:code;type:varchar;" json:"code"`               //код продукта
	Description string `gorm:"column:description;type:varchar;" json:"description"` //Описание
} //@name FinService

func (FinService) TableName() string {
	return "tb_fin_services"
}

type System struct {
	Id     string `gorm:"column:id;type:varchar;" json:"id"`           //код
	NameEn string `gorm:"column:name_en;type:varchar;" json:"name_en"` //Наименование EN
	NameKz string `gorm:"column:name_kz;type:varchar;" json:"name_kz"` //Наименование KZ
	NameRu string `gorm:"column:name_ru;type:varchar;" json:"name_ru"` //Наименование RU
} //@name System

func (System) TableName() string {
	return "tb_system"
}

type EventType struct {
	Id     int    `gorm:"primaryKey" json:"id"`                                    //код
	Name   string `gorm:"column:event_type;type:varchar;" json:"event_type"`       //Наименование
	NameKz string `gorm:"column:event_type_kz;type:varchar;" json:"event_type_kz"` //Наименование
} //@name EventType

func (EventType) TableName() string {
	return "tb_event_types"
}

type Spr struct {
	Name     string `gorm:"column:name;type:varchar;" json:"name"`      //наименование справочника
	Key      string `gorm:"column:key;type:varchar;" json:"key"`        //ключ
	Value    string `gorm:"column:value;type:varchar;" json:"value"`    //значение
	Disabled bool   `gorm:"column:disabled;type:bool;" json:"disabled"` //отключено
} //@name Spr

func (Spr) TableName() string {
	return "tb_spr"
}
