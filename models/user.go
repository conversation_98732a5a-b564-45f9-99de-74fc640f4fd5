package models

import (
	"fmt"
	"os"
	"strings"
	"time"
	"unicode"

	"github.com/dgrijalva/jwt-go"
	"github.com/gofiber/fiber/v2"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"projects.post.kz/antifraud-online/tool"
)

//swagger:model user
type User struct {
	// swagger:ignore
	Id         int64   `json:"id"`
	FirstName  string  `json:"first_name"`
	LastName   string  `json:"last_name"`
	Patronymic *string `json:"patronymic"`
	Email      string  `json:"email" gorm:"<-:create"`
	// swagger:ignore
	Password string `json:"password" gorm:"<-:create"`
	RoleId   int64  `json:"role_id" gorm:"<-:create"` // 1 - Admin 2 - Moderator 3 - innovator 4 - PC 5 - user
	// swagger:ignore
	Role *Role `json:"role" gorm:"->"`
	// swagger:ignore
	HashExpirationTime time.Time `json:"-" `
	// swagger:ignore
	Hash *string `json:"-"`
	// swagger:ignore
	ClaimToken string `json:"claim_token" gorm:"-"`
	// swagger:ignore
	CreatedAt time.Time `json:"-"`
	// swagger:ignore
	UpdatedAt time.Time `json:"-"`
	// swagger:ignore
	jwt.StandardClaims `gorm:"-" json:"-"`
}

func (User) TableName() string {
	return fmt.Sprintf("%s.%s", SchemaAdminPanel, "user")
}

func (u User) UniqueFieldName() string {
	tbName := u.TableName()
	return fmt.Sprintf(`%[1]s.id as id,
								%[1]s.first_name,
								%[1]s.last_name,
								%[1]s.patronymic,
								%[1]s.email,
								%[1]s.role_id
								`, tbName)
}

//swagger:model Credentials
type Credentials struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

func VerifyUserCredentials(cred Credentials, tx *gorm.DB) (u User, warning string, err error) {
	if err = VerifyEmailMask(cred.Email); err != nil {
		return u, "", fiber.NewError(fiber.StatusBadRequest, err.Error())
	}
	//if err = VerifyPasswordMask(cred.Password); err != nil {
	//	return u, fiber.NewError(fiber.StatusBadRequest, err.Error())
	//}
	roleTbName := Role{}.TableName()
	roleFields := Role{}.UniqueFieldName()
	userTbName := User{}.TableName()
	j := fmt.Sprintf(`INNER JOIN %[1]s on %[1]s.id = %[2]s.role_id `, roleTbName, userTbName)
	q := fmt.Sprintf(`%s, %s.*`, roleFields, userTbName)
	var userHasOne struct {
		User
		Role *Role `gorm:"embedded;embeddedPrefix:_role_"`
	}
	err = tx.
		Table(userTbName).
		Select(q).
		Joins(j).
		Where("email = ?", cred.Email).
		First(&userHasOne).
		Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return u, "", err
	}
	if err == gorm.ErrRecordNotFound {
		return u, "", fiber.NewError(fiber.StatusUnauthorized, "Зарегистрируйтесь")
	}
	u = userHasOne.User
	u.Role = userHasOne.Role
	err = bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(cred.Password))
	if err != nil {
		if u.HashExpirationTime.After(time.Now()) {
			err = bcrypt.CompareHashAndPassword([]byte(*u.Hash), []byte(cred.Password))
			if err != nil {
				return u, "", fiber.NewError(fiber.StatusUnauthorized, ERRSignIn)
			}
			warning = "временный пароль необходимо изменить"
		} else {
			if u.Hash != nil {
				return u, "", fiber.NewError(fiber.StatusUnauthorized, "срок годности временного пароля истек")
			}
		}
		if warning == "" {
			return u, "", fiber.NewError(fiber.StatusUnauthorized, ERRSignIn)
		}
	}
	u.Password = ""
	return
}

func VerifyEmailMask(e string) error {
	if len(e) == 0 {
		return fmt.Errorf("обязательное поле: email")
	}
	if !strings.Contains(e, "@") {
		return fmt.Errorf("почта должна содержать @")
	}

	if !strings.Contains(strings.Split(e, "@")[1], ".") {
		return fmt.Errorf("почта содержать домен после @ (mail.ru)")
	}
	if len(e) == 0 {
		return fmt.Errorf("обязательное поле: пароль")
	}
	return nil
}

func (u User) CreateJwtToken() (tokenString string, err error) {
	u.StandardClaims = jwt.StandardClaims{
		ExpiresAt: time.Now().Add(time.Duration(72) * time.Hour).Unix(),
		Issuer:    "sp",
		Subject:   fmt.Sprintf("%s", u.Email),
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, u)
	tokenString, err = token.SignedString([]byte(os.Getenv("token_secret")))
	if err != nil {
		return "", err
	}
	return
}

func (u *User) Validate(tx *gorm.DB) (err error) {
	if err = VerifyEmailMask(u.Email); err != nil {
		return fiber.NewError(fiber.StatusBadRequest, err.Error())
	}
	if len(u.FirstName) == 0 {
		return fiber.NewError(fiber.StatusBadRequest, "обязательное поле имя")
	}
	if len(u.LastName) == 0 {
		return fiber.NewError(fiber.StatusBadRequest, "обязательное поле фамилия")
	}
	if u.RoleId == 0 {
		return fiber.NewError(fiber.StatusBadRequest, "обязательное поле роль")
	}
	if err = CheckEmail(u.Email, tx); err != nil {
		return
	}
	return
}

func (u *User) EncryptPassword() error {
	if hashedPassword, err := bcrypt.GenerateFromPassword([]byte(u.Password), bcrypt.DefaultCost); err != nil {
		return fiber.NewError(fiber.StatusBadRequest, "обязательное поле роль")
	} else {
		u.Password = string(hashedPassword)
	}
	return nil
}

func (u *User) Create(tx *gorm.DB) (err error) {
	if err = tx.Omit(clause.Associations).Create(&u).Error; err != nil {
		return err
	}
	return
}

func (u *User) GetRole(tx *gorm.DB) (err error) {
	if err = tx.Where("id = ?", u.RoleId).First(&u.Role).Error; err != nil {
		return err
	}
	if err == gorm.ErrRecordNotFound {
		return fiber.NewError(fiber.StatusUnauthorized, ERRSignIn)
	}
	return nil
}

func (u *User) VerifyRole(tx *gorm.DB) (err error) {
	var count int64
	if u.RoleId == RoleIdAdmin {
		return fiber.NewError(fiber.StatusBadRequest, "роль не доступна")
	}
	err = tx.Model(&Role{}).Where("id = ?", u.RoleId).Count(&count).Error
	if count == 0 {
		return fiber.NewError(fiber.StatusBadRequest, "роль отсутствует")
	}
	return
}

func VerifyPasswordMask(p string) error {
	var (
		uppercasePresent bool
		lowercasePresent bool
		numberPresent    bool
		symbolPresent    bool
		passLen          int
	)
	for _, ch := range p {
		switch {
		case unicode.IsNumber(ch):
			numberPresent = true
			passLen++
		case unicode.IsUpper(ch):
			uppercasePresent = true
			passLen++
		case unicode.IsLower(ch):
			lowercasePresent = true
			passLen++
		case unicode.IsPunct(ch) || unicode.IsSymbol(ch):
			symbolPresent = true
			passLen++
		case ch == ' ':
			passLen++
		}
	}
	if !lowercasePresent {
		return fmt.Errorf("пароль должен содержать хотя бы 1 символ в нижнем регистре")
	}
	if !uppercasePresent {
		return fmt.Errorf("пароль должен содержать хотя бы 1 символ в верхнем регистре")
	}
	if !numberPresent {
		return fmt.Errorf("пароль должен содержать хотя бы 1 цифру")
	}
	if !(minPassLength <= passLen && passLen <= maxPassLength) {
		return fmt.Errorf(fmt.Sprintf("длинна пароля должна быть между %d и %d символами", minPassLength, maxPassLength))
	}
	if !symbolPresent {
		return fmt.Errorf("пароль должен содержать хотя бы 1 специальный символ")
	}
	return nil
}

type UserHasOne struct {
	LUser
	Role Role `json:"role" gorm:"embedded;embeddedPrefix:_role_;"`
}

func UserList(c *fiber.Ctx, tx *gorm.DB) (res tool.PaginationResult, err error) {
	res = tool.SetPagination(c)

	roleTbName := Role{}.TableName()
	roleFields := Role{}.UniqueFieldName()
	//userFields := User{}.UniqueFieldName()
	userTbName := User{}.TableName()

	j := fmt.Sprintf(`INNER JOIN %[1]s on %[1]s.id = %[2]s.role_id `, roleTbName, userTbName)
	q := fmt.Sprintf(`%s, %s.*`, roleFields, userTbName)

	tx = tx.
		Scopes(tool.SetFilters(c)).
		Scopes(tool.SetSort(c))

	var userHasOne []UserHasOne

	tx = tx.
		Table(userTbName).
		Select(q).
		Joins(j).
		Scopes(tool.Paginate(c)).
		Find(&userHasOne)

	res.Count = tx.RowsAffected
	res.Items = userHasOne
	return
}

func CheckEmail(email string, tx *gorm.DB) (err error) {
	tx = tx.
		Model(User{}).
		Where("email = ?", email)
	if tx.RowsAffected > 0 {
		return fiber.NewError(fiber.StatusBadRequest, "email уже существует")
	}
	return
}

type LUser struct {
	// swagger:ignore
	Id         int64   `json:"id"`
	FirstName  string  `json:"first_name"`
	LastName   string  `json:"last_name"`
	Patronymic *string `json:"patronymic"`
	Email      string  `json:"email" gorm:"<-:create"`
	// swagger:ignore
	Password string `json:"password" gorm:"<-:create"`
	RoleId   int64  `json:"role_id" gorm:"<-:create"` // 1 - Admin 2 - Moderator 3 - innovator 4 - PC 5 - user
	// swagger:ignore
	Role *Role `json:"-" gorm:"->"`
	// swagger:ignore
	HashExpirationTime time.Time `json:"-" `
	// swagger:ignore
	Hash *string `json:"-"`
	// swagger:ignore
	ClaimToken string `json:"claim_token" gorm:"-"`
	// swagger:ignore
	CreatedAt time.Time `json:"-"`
	// swagger:ignore
	UpdatedAt time.Time `json:"-"`
	// swagger:ignore
	jwt.StandardClaims `gorm:"-" json:"-"`
}
