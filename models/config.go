package models

import (
	"encoding/json"
	//"fmt"
	"os"
)

type Configuration struct {
	SolutionResponse string
}

var Config Configuration

func (c *Configuration) Load() {
	file, _ := os.Open("config.json")
	decoder := json.NewDecoder(file)
	err := decoder.Decode(&c)

	if err != nil {
		panic("Cannot load config")
	}

}

func getEnv(key string, defaultVal string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultVal
}
