package models

import (
	"fmt"
	"github.com/gofiber/fiber/v2"
)

//swagger:model role
type Role struct {
	Id       int64  `json:"id"`
	NameRu   string `json:"name_ru"`
	NameKZ   string `json:"name_kz"`
	NameEn   string `json:"name_en"`
	MainName string `json:"main_name"`
}

func (Role) TableName() string {
	return fmt.Sprintf("%s.%s", SchemaAdminPanel, "role")
}

func (r Role) UniqueFieldName() string {
	tbName := r.TableName()
	prefix := "_role"
	return fmt.Sprintf(`%[1]s.id as %[2]s_id,
								%[1]s.name_ru as %[2]s_name_ru,
								%[1]s.name_kz as %[2]s_name_kz,
								%[1]s.name_en as %[2]s_name_en,
								%[1]s.main_name as %[2]s_main_name
								`, tbName, prefix)
}

func (r *Role) Validation() (err error) {
	if r.NameRu == "" {
		return fiber.NewError(fiber.StatusBadRequest, "не введено поле наименование на русском")
	}
	if r.NameKZ == "" {
		return fiber.NewError(fiber.StatusBadRequest, "не введено поле наименование на казахском")
	}
	if r.NameEn == "" {
		return fiber.NewError(fiber.StatusBadRequest, "не введено поле наименование на английском")
	}
	if r.MainName == "" {
		return fiber.NewError(fiber.StatusBadRequest, "не введено поле основное наименование")
	}

	return
}
