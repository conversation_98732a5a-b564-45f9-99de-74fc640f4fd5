package models

import (
	"github.com/elastic/go-elasticsearch/v7"
	"log"
)

var Es *elasticsearch.Client

func NewElastic(env *Environment) (es *elasticsearch.Client, err error) {
	cfg := elasticsearch.Config{
		Username: env.<PERSON>astic<PERSON>ser,
		Addresses: []string{
			env.ConnectionElasticString,
		},
		Password: env.ElasticPass,
	}
	Es, err = elasticsearch.NewClient(cfg)
	if err != nil {
		return
	}
	log.Println("connect to elastic")
	return Es, err
}

func GetEs() *elasticsearch.Client {
	return Es
}
