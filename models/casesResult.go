package models

type CasesResult struct {
	ID                 int     `json:"id"`                   // Идентификатор кейса
	CreationDate       string  `json:"creation_date"`        // Дата создания кейса
	FinishAssignedDate string  `json:"finish_assigned_date"` // Дата завершения назначения
	FullName           string  `json:"full_name"`            // Полное имя пользователя
	IIN                string  `json:"iin"`                  // ИИН пользователя
	Score              int     `json:"score"`                // Оценка
	AssignedUser       string  `json:"assigned_user"`        // Назначенный пользователь
	ProductID          string  `json:"product_id"`           // Продукт
	AmountKZT          float64 `json:"amount_kzt"`           // Сумма в тенге
	Decision           string  `json:"decision"`             // Решение
}
