package middleware

import (
	"admin-panel/models"
	"net/http"
	"strings"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/gofiber/fiber/v2"
)

func AuthMiddleware(role ...string) gin.HandlerFunc {
	return func(c *gin.Context) {

		err := VerifyToken(c, role...)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
			c.Abort()
			return
		}

		c.Next()
	}
}
func VerifyToken(c *gin.Context, role ...string) (err error) {
	authorization := c.GetHeader("Authorization")
	if authorization == "" {
		return fiber.NewError(fiber.StatusUnauthorized, "неверный авторизационный заголовок, code:001")
	}
	tokenString := strings.Replace(authorization, "Bearer ", "", -1)
	if tokenString == "" {
		return fiber.NewError(fiber.StatusUnauthorized, "неверный авторизационный заголовок, code:002")
	}
	token, err := jwt.ParseWithClaims(tokenString, &models.User{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(models.Secret), nil
	})
	if err != nil {
		return fiber.NewError(fiber.StatusUnauthorized, err.Error()+", неверный авторизационный заголовок, code:003")
	}
	user, ok := token.Claims.(*models.User)
	if !token.Valid || !ok {
		return fiber.NewError(fiber.StatusUnauthorized, "неверный авторизационный заголовок, code:004")
	}

	for i := 0; i < len(role); i++ {
		if user.Role.MainName != role[i] {
			return fiber.NewError(fiber.StatusForbidden, "нет досутпа код: 111")
		}
	}

	return
}
