package handlers

import (
	"admin-panel/db"
	"admin-panel/models"
	"errors"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"projects.post.kz/antifraud-online/tool"
)

// GetRelatedTransfers Получение связанных транзацкий по ИИН
func GetRelatedTransfers(c *gin.Context) {

	var (
		rows               []models.RelatedTransfers
		startDate, endDate time.Time
		err                error
		ResponseJSONData   models.ResponseJSONData
	)

	if err := c.BindJSON(&ResponseJSONData); err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
			return
		}
	}
	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
			return
		}
	}
	result := tool.SetPaginationGin(c)

	tx := db.DBConn.Table("tb_event_person ep").
		Select(`
               efo.oper_date_time, 
			   ep.iin, 
			   ep.person_dc, 
			   efo.product_id,
			    efod.amount_kzt, 
				efod.card_number,
				efo.transaction_status`).
		Joins("JOIN tb_event_fin_operation efo on efo.event_id = ep.event_id").
		Joins("JOIN tb_event_fin_oper_details efod on efo.id = efod.operation_id").
		Where("ep.iin = ?", ResponseJSONData.IIN).
		Where("efod.oper_dc = ep.person_dc").
		Where("ep.person_dc IN ?", []int{1, 2}) //1 Отправитель, 2 Получатель
	if !startDate.IsZero() {
		tx = tx.Where("efo.oper_date_time >= ?", startDate)
	}
	if !endDate.IsZero() {
		tx = tx.Where("efo.oper_date_time <= ?", endDate)
	}

	tx = tx.Count(&result.Count).
		Scopes(tool.PaginateGin(c)).
		Scopes(tool.SetSortGin(c)).
		Scopes(tool.SetFiltersGin(c)).
		Find(&rows)

	if errors.Is(tx.Error, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, tx.Error))
		return
	}

	if tx.Error != nil {
		c.JSON(400, response(nil, tx.Error))
		return
	}
	result.Items = rows
	c.JSON(200, response(result, nil))
	return

}
