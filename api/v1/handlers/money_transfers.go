package handlers

import (
	"admin-panel/db"
	"admin-panel/models"
	"errors"
	"fmt"
	"github.com/xuri/excelize/v2"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"projects.post.kz/antifraud-online/tool"
)

func GetMoneyTransfers(c *gin.Context) {

	var (
		rows               []models.MoneyTransfers
		startDate, endDate time.Time
		err                error
	)
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
			return
		}
	}
	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
			return
		}
	}
	result := tool.SetPaginationGin(c)

	tx := db.DBConn.Table("tb_event_fin_operation as tefo").
		Select(`
		tefo.id as id,
		tp.iin as sender_iin,
		tp2.iin as recipient_iin,
		tefo.oper_date_time,
		tefo.transaction_status,
		d1.amount_kzt,
		d1.card_number as sender_card_number,
		d2.card_number as recip_card_number`).
		Joins("JOIN tb_event_person as tp2 ON tp2.operation_id = tefo.id AND tp2.person_dc = 2").
		Joins("JOIN tb_event_person as tp ON tp.operation_id = tefo.id AND tp.person_dc = 1").
		Joins("JOIN tb_event_fin_oper_details as d1 ON d1.operation_id = tefo.id AND d1.oper_dc = 1").
		Joins("JOIN tb_event_fin_oper_details as d2 ON d2.operation_id = tefo.id AND d2.oper_dc = 2").
		Where("tefo.product_id = ?", "MONEY_SEND").
		Where("tefo.transaction_status IN ?", []int{0, 1, 3}) //1 успешно, 3 ошибка
	if !startDate.IsZero() {
		tx = tx.Where("tefo.oper_date_time >= ?", startDate)
	}
	if !endDate.IsZero() {
		tx = tx.Where("tefo.oper_date_time <= ?", endDate)
	}

	tx = tx.Count(&result.Count).
		Scopes(tool.PaginateGin(c)).
		Scopes(tool.SetSortGin(c)).
		Scopes(tool.SetFiltersGin(c)).
		Find(&rows)

	if errors.Is(tx.Error, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, tx.Error))
		return
	}

	if tx.Error != nil {
		c.JSON(400, response(nil, tx.Error))
		return
	}
	result.Items = rows
	c.JSON(200, response(result, nil))
	return

}

func ExportMoneyTransfersToExcel(c *gin.Context) {
	var (
		moneyTransfersResult []models.MoneyTransfers
		startDate, endDate   time.Time
		err                  error
	)
	startDateStr := c.DefaultQuery("start_date", "")
	endDateStr := c.DefaultQuery("end_date", "")

	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
			return
		}
	}
	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
			return
		}
	}
	tx := db.DBConn.Table("tb_event_fin_operation as tefo").
		Select(`
		tefo.id as id,
		tp.iin as sender_iin,
		tp2.iin as recipient_iin,
		tefo.oper_date_time,
		tefo.transaction_status,
		d1.amount_kzt,
		d1.card_number as sender_card_number,
		d2.card_number as recip_card_number`).
		Joins("JOIN tb_event_person as tp2 ON tp2.operation_id = tefo.id AND tp2.person_dc = 2").
		Joins("JOIN tb_event_person as tp ON tp.operation_id = tefo.id AND tp.person_dc = 1").
		Joins("JOIN tb_event_fin_oper_details as d1 ON d1.operation_id = tefo.id AND d1.oper_dc = 1").
		Joins("JOIN tb_event_fin_oper_details as d2 ON d2.operation_id = tefo.id AND d2.oper_dc = 2").
		Where("tefo.product_id = ?", "MONEY_SEND")

	if !startDate.IsZero() {
		tx = tx.Where("tefo.oper_date_time >= ?", startDate)
	}
	if !endDate.IsZero() {
		tx = tx.Where("tefo.oper_date_time <= ?", endDate)
	}

	if err := tx.Find(&moneyTransfersResult).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch money transfers"})
		return
	}

	f := excelize.NewFile()
	sheet := "Sheet1"

	headers := []string{"ID кейса", "Дата и время создания", "ИИН", "ФИО", "Сумма (KZT)", "Карта отправителя", "Карта получателя", "Статус"}
	columns := []string{"A", "B", "C", "D", "E", "F", "G", "H"}

	for i, header := range headers {
		f.SetCellValue(sheet, fmt.Sprintf("%s1", columns[i]), header)
	}

	for idx, moneyTransfer := range moneyTransfersResult {
		row := idx + 2
		f.SetCellValue(sheet, fmt.Sprintf("A%d", row), moneyTransfer.ID)
		f.SetCellValue(sheet, fmt.Sprintf("B%d", row), moneyTransfer.OperDateTime)
		f.SetCellValue(sheet, fmt.Sprintf("C%d", row), moneyTransfer.SenderIIN)
		f.SetCellValue(sheet, fmt.Sprintf("D%d", row), moneyTransfer.RecipientIIN)
		f.SetCellValue(sheet, fmt.Sprintf("E%d", row), moneyTransfer.AmountKzt)
		f.SetCellValue(sheet, fmt.Sprintf("F%d", row), moneyTransfer.SenderCardNumber)
		f.SetCellValue(sheet, fmt.Sprintf("G%d", row), moneyTransfer.RecipientIIN)
		f.SetCellValue(sheet, fmt.Sprintf("H%d", row), moneyTransfer.TransactionStatus)
	}

	for _, col := range columns {
		f.SetColWidth(sheet, col, col, 20)
	}

	filename := "money_transfers.xlsx"
	if !startDate.IsZero() && !endDate.IsZero() {
		filename = fmt.Sprintf("money_transfers %s - %s.xlsx", startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
	}

	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	if err := f.Write(c.Writer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to write file"})
	}
}
