package handlers

import (
	"admin-panel/db"
	"admin-panel/services"
	"errors"
	"fmt"
	"github.com/xuri/excelize/v2"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"projects.post.kz/antifraud-online/tool"
)

func response(data interface{}, err error) map[string]interface{} {
	var (
		m       = make(gin.H)
		errText string
		code    = 1
	)

	if err != nil {
		errText = err.Error()
		code = -1
	}

	m["error_code"] = code
	m["error"] = errText

	if data != nil {
		m["result"] = data
	}

	return m
}

// GetCases godoc
// @Summary Получение списка кейсов
// @Tags cases
// @Produce json
// @Param page query string false "page"
// @Param per_page query string false "per_page"
// @Param sort query string false "Сортировка asc/desc" Enums(asc, desc)
// @Param sort_field query string false "Поля для сортировки через запятую"
// @Param filter_values query string false "Значение поля для фильтрации, через запятую (в паре с filter_field)"
// @Param filter_fields query string false "Название полей для фильтрации, через запятую (в паре с filter_value)"
// @Param filter_type query string false "Тип фильтра полное совпадение (=) или совпадение по like" Enums(=, like)
// @Param filter_logic query string false "Пересечение или дополнение" Enums(and, or)
// @Success 200 {object} services.CaseExternal
// @Router /case/list [get]
func GetCases(c *gin.Context) {
	var (
		cases       []services.Case
		casesHasOne []services.CaseExternal
	)

	tblEvent := services.Event{}.TableName()

	result := tool.SetPaginationGin(c)
	tx := tool.SetPreloadGin(db.DBConn, []string{"CaseFiles"}).Preload("CaseDetails", "score != ? OR scenario_desc != ?", 0, "")

	tx = tx.
		Scopes(tool.SetFiltersGin(c)).
		Scopes(tool.SetSortGin(c))

	tx = tx.
		Model(services.Case{}).
		Group("tb_cases.id").
		Count(&result.Count).
		Scopes(tool.PaginateGin(c)).
		Find(&cases)

	if errors.Is(tx.Error, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, tx.Error))
		return
	}

	if tx.Error != nil {
		c.JSON(400, response(nil, tx.Error))
		return
	}

	for _, val := range cases {
		var eventStatus int
		db.DBConn.Table(tblEvent).Select("status").Where("req_id = ?", val.EventId).Scan(&eventStatus)

		casesHasOne = append(casesHasOne, services.CaseExternal{
			Case:        val,
			EventStatus: eventStatus,
		})
	}

	result.Items = casesHasOne

	c.JSON(200, response(result, nil))
	return
}

// UpdateCase godoc
// @Summary Изменение кейса
// @Tags cases
// @Produce json
// @Param Req body services.Case true "Add request"
// @Success 200 {object} services.Case
// @Router /case [put]
func UpdateCase(c *gin.Context) {
	var cs services.Case

	err := c.BindJSON(&cs)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	tx := db.DBConn.Begin()

	cf, err := services.SaveCaseFiles(tx, cs)
	if err != nil {
		tx.Rollback()
		c.JSON(400, response(nil, err))
		return
	}

	cs.UpdateDate = time.Now()

	if cs.Decision == 1 || cs.Decision == 3 {
		cs.FinishDueDate = time.Now()
	}

	err = tx.Save(&cs).Error

	if err != nil {
		tx.Rollback()
		c.JSON(400, response(nil, err))
		return
	}

	err = tx.Commit().Error
	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	cs.CaseFiles = cf

	err = services.UpdateEvent(cs)
	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(cs, nil))
	return
}

// AssignedTo godoc
// @Summary Переназначить кейс
// @Tags cases
// @Produce json
// @Param Req body services.Case true "Add request"
// @Success 200 {object} services.Case
// @Router /case/assign [patch]
func AssignedTo(c *gin.Context) {
	var cs services.Case

	err := c.BindJSON(&cs)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	csOld, err := services.GetCaseById(cs.Id)

	csOld.UpdateDate = time.Now()
	csOld.AssignedUser = cs.AssignedUser
	csOld.StartAssignedDate = cs.StartAssignedDate
	csOld.FinishAssignedDate = cs.FinishAssignedDate

	err = db.DBConn.Save(&csOld).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(csOld, nil))
	return
}

// GetCaseComments godoc
// @Summary Получение списка комментов кейсов
// @Tags cases
// @Produce json
// @Param id path string true "case id"
// @Success 200 {object} services.CaseComment
// @Router /case/comment/{id} [get]
func GetCaseComments(c *gin.Context) {

	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(400, response(nil, fmt.Errorf("Invalid case id")))
		return
	}

	list, err := services.GetCaseComments(id)

	if errors.Is(err, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, err))
		return
	}

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(list, nil))
	return
}

// AddCaseComment godoc
// @Summary Добавление коммента к кейсу
// @Tags cases
// @Produce json
// @Param Req body services.CaseComment true "Add request"
// @Success 200 {object} services.CaseComment
// @Router /case/comment [post]
func AddCaseComment(c *gin.Context) {
	var com services.CaseComment

	err := c.BindJSON(&com)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	com.CreationDate = time.Now()
	com.UpdateDate = time.Now()
	com.IsActive = true

	err = db.DBConn.Create(&com).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(com, nil))
	return
}

// DelCaseComment godoc
// @Summary Добавление коммента к кейсу
// @Tags cases
// @Produce json
// @Param id path string true "comment id"
// @Success 200 {object} services.CaseComment
// @Router /case/comment/{id} [delete]
func DelCaseComment(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(400, response(nil, fmt.Errorf("Invalid comment id")))
		return
	}

	com, err := services.GetCaseComment(id)

	com.UpdateDate = time.Now()
	com.IsActive = false

	err = db.DBConn.Save(&com).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(com, nil))
	return
}

// UpdateCaseComment godoc
// @Summary Изменение коммента к кейсу
// @Tags cases
// @Produce json
// @Param Req body services.CaseComment true "Add request"
// @Success 200 {object} services.CaseComment
// @Router /case/comment [put]
func UpdateCaseComment(c *gin.Context) {
	var com services.CaseComment

	err := c.BindJSON(&com)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	comOld, err := services.GetCaseComment(com.Id)

	comOld.UpdateDate = time.Now()
	comOld.Comment = com.Comment

	err = db.DBConn.Save(&comOld).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(com, nil))
	return
}

// CasesDetailed godoc
// @Summary Получение списка кейсов со связанными данными
// @Tags cases
// @Produce json
// @Param page query string false "page"
// @Param per_page query string false "per_page"
// @Param sort query string false "Сортировка asc/desc" Enums(asc, desc)
// @Param sort_field query string false "Поля для сортировки через запятую"
// @Param filter_values query string false "Значение поля для фильтрации, через запятую (в паре с filter_field)"
// @Param filter_fields query string false "Название полей для фильтрации, через запятую (в паре с filter_value)"
// @Param filter_type query string false "Тип фильтра полное совпадение (=) или совпадение по like" Enums(=, like)
// @Param filter_logic query string false "Пересечение или дополнение" Enums(and, or)
// @Success 200 {object} services.CaseExternal
// @Router /case/list [get]
func CasesDetailed(c *gin.Context) {
	var (
		casesResult        []services.CasesResult
		startDate, endDate time.Time
		err                error
	)

	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
			return
		}
	}
	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
			return
		}
	}

	result := tool.SetPaginationGin(c)

	tx := db.DBConn.Table("tb_cases c").
		Select("c.id, c.creation_date, c.finish_assigned_date, ep.iin, ep.full_name, c.score, c.assigned_user, efo.product_id, efod.amount_kzt, c.decision").
		Joins("JOIN tb_event_fin_operation efo ON c.event_id = efo.event_id").
		Joins("JOIN tb_event_fin_oper_details efod ON efo.id = efod.operation_id").
		Joins("JOIN tb_event_person ep ON c.event_id = ep.event_id").
		Where("ep.person_dc = ? AND efod.oper_dc = ?", 1, 1)

	if !startDate.IsZero() {
		tx = tx.Where("c.creation_date >= ?", startDate)
	}
	if !endDate.IsZero() {
		tx = tx.Where("c.creation_date <= ?", endDate)
	}

	tx = tx.Count(&result.Count).
		Scopes(tool.PaginateGin(c)).
		Scopes(tool.SetSortGin(c)).
		Scopes(tool.SetFiltersGin(c)).
		Find(&casesResult)

	if errors.Is(tx.Error, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, tx.Error))
		return
	}

	if tx.Error != nil {
		c.JSON(400, response(nil, tx.Error))
		return
	}

	result.Items = casesResult

	c.JSON(200, response(result, nil))
	return
}
func ExportCasesToExcel(c *gin.Context) {
	var (
		casesResult        []services.CasesResult
		startDate, endDate time.Time
		err                error
	)

	startDateStr := c.DefaultQuery("start_date", "")
	endDateStr := c.DefaultQuery("end_date", "")

	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
			return
		}
	}
	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
			return
		}
	}

	tx := db.DBConn.Table("tb_cases c").
		Select("c.id, c.creation_date, c.finish_assigned_date, ep.iin, ep.full_name, c.score, c.assigned_user, efo.product_id, efod.amount_kzt, c.decision").
		Joins("JOIN tb_event_fin_operation efo ON c.event_id = efo.event_id").
		Joins("JOIN tb_event_fin_oper_details efod ON efo.id = efod.operation_id").
		Joins("JOIN tb_event_person ep ON c.event_id = ep.event_id").
		Where("ep.person_dc = ? AND efod.oper_dc = ?", 1, 1)

	if !startDate.IsZero() {
		tx = tx.Where("c.creation_date >= ?", startDate)
	}
	if !endDate.IsZero() {
		tx = tx.Where("c.creation_date <= ?", endDate)
	}

	if err := tx.Find(&casesResult).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch cases"})
		return
	}

	f := excelize.NewFile()
	sheet := "Sheet1"

	headers := []string{"ID кейса", "Дата и время создания", "ИИН", "ФИО", "Скоринг балл", "Идентификатор продукта", "Сумма KZT", "Пользователь", "Статус"}
	columns := []string{"A", "B", "C", "D", "E", "F", "G", "H", "I"}

	for i, header := range headers {
		f.SetCellValue(sheet, fmt.Sprintf("%s1", columns[i]), header)
	}

	for idx, caseResult := range casesResult {
		row := idx + 2
		f.SetCellValue(sheet, fmt.Sprintf("A%d", row), caseResult.ID)
		f.SetCellValue(sheet, fmt.Sprintf("B%d", row), caseResult.CreationDate)
		f.SetCellValue(sheet, fmt.Sprintf("C%d", row), caseResult.IIN)
		f.SetCellValue(sheet, fmt.Sprintf("D%d", row), caseResult.FullName)
		f.SetCellValue(sheet, fmt.Sprintf("E%d", row), caseResult.Score)
		f.SetCellValue(sheet, fmt.Sprintf("F%d", row), caseResult.ProductID)
		f.SetCellValue(sheet, fmt.Sprintf("G%d", row), caseResult.AmountKZT)
		f.SetCellValue(sheet, fmt.Sprintf("H%d", row), caseResult.AssignedUser)
		f.SetCellValue(sheet, fmt.Sprintf("I%d", row), caseResult.Decision)
	}

	for _, col := range columns {
		f.SetColWidth(sheet, col, col, 20)
	}

	filename := "cases.xlsx"
	if !startDate.IsZero() && !endDate.IsZero() {
		filename = fmt.Sprintf("cases %s - %s.xlsx", startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
	}

	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	if err := f.Write(c.Writer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to write file"})
	}
}
