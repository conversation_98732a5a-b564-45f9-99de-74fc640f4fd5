package handlers

import (
	"admin-panel/db"
	"admin-panel/models"
	"errors"
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	tool "projects.post.kz/antifraud-online/tool"
)

func GetListCardBlackList(c *gin.Context) {
	result, err := tool.GetListGin(models.CardBlackList{}, c, db.DBConn, []string{})

	if errors.Is(err, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, err))
		return
	}

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(result, nil))
	return
}

func CreateCardBlackList(c *gin.Context) {
	var card models.CardBlackList

	if err := c.Bind<PERSON>(&card); err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	if err := db.DBConn.Create(&card).Error; err != nil {
		c.JSO<PERSON>(503, response(nil, err))
		return
	}

	c.JSO<PERSON>(200, response(card, nil))
	return
}

func UpdateCardBlackList(c *gin.Context) {
	var card models.CardBlackList

	if err := c.BindJSON(&card); err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	if err := db.DBConn.Where("id = ?", card.Id).UpdateColumns(&card).Error; err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	c.JSON(200, response(card, nil))
	return
}

func DeleteCardBlackList(c *gin.Context) {
	id, err := strconv.Atoi(c.Query("id"))
	if err != nil {
		c.JSON(400, response(nil, fmt.Errorf("Invalid card black list id")))
		return
	}

	err = db.DBConn.Where("id = ?", id).Delete(&models.CardBlackList{}).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, err))
		return
	}

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	c.JSON(200, response("deleted", nil))
	return
}
