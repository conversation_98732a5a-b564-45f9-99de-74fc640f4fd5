package handlers

import (
	"admin-panel/db"
	"admin-panel/models"
	"errors"
	"fmt"
	"github.com/xuri/excelize/v2"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"projects.post.kz/antifraud-online/tool"
)

func GetSMP(c *gin.Context) {

	var (
		rows               []models.SMP
		startDate, endDate time.Time
		err                error
	)

	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
			return
		}
	}
	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
			return
		}
	}
	result := tool.SetPaginationGin(c)

	tx := db.DBConn.Table("tb_event_person tp").
		Select("tp.event_id, tefo.oper_date_time, tefo.product_id, tefod.amount_kzt, tp.iin, tp.full_name, tp.person_type, tp.mobile_number, tefod.bank_name, tefod.lic_account").
		Joins("JOIN tb_event_fin_operation tefo ON tefo.id = tp.operation_id").
		Joins("JOIN tb_event_fin_oper_details tefod ON tefo.id = tefod.operation_id").
		Where("tp.person_dc = ?", 1).
		Where("tefod.oper_dc = ?", 2).
		Where("tefo.product_id = ?", "SMP")

	if !startDate.IsZero() {
		tx = tx.Where("tefo.oper_date_time >= ?", startDate)
	}
	if !endDate.IsZero() {
		tx = tx.Where("tefo.oper_date_time <= ?", endDate)
	}

	tx = tx.Count(&result.Count).
		Scopes(tool.PaginateGin(c)).
		Scopes(tool.SetSortGin(c)).
		Scopes(tool.SetFiltersGin(c)).
		Find(&rows)

	if errors.Is(tx.Error, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, tx.Error))
		return
	}

	if tx.Error != nil {
		c.JSON(400, response(nil, tx.Error))
		return
	}
	result.Items = rows
	c.JSON(200, response(result, nil))
	return

}

func ExportSMPToExcel(c *gin.Context) {
	var (
		smpResult          []models.SMP
		startDate, endDate time.Time
		err                error
	)

	startDateStr := c.DefaultQuery("start_date", "")
	endDateStr := c.DefaultQuery("end_date", "")

	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
			return
		}
	}
	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
			return
		}
	}
	tx := db.DBConn.Table("tb_event_person tp").
		Select("tp.event_id, tefo.oper_date_time, tefo.product_id, tefod.amount_kzt, tp.iin, tp.full_name, tp.person_type, tp.mobile_number, tefod.bank_name, tefod.lic_account").
		Joins("JOIN tb_event_fin_operation tefo ON tefo.id = tp.operation_id").
		Joins("JOIN tb_event_fin_oper_details tefod ON tefo.id = tefod.operation_id").
		Where("tp.person_dc = ?", 1).
		Where("tefod.oper_dc = ?", 2).
		Where("tefo.product_id = ?", "SMP")

	if !startDate.IsZero() {
		tx = tx.Where("tefo.oper_date_time >= ?", startDate)
	}
	if !endDate.IsZero() {
		tx = tx.Where("tefo.oper_date_time <= ?", endDate)
	}

	if err := tx.Find(&smpResult).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch smp"})
		return
	}

	f := excelize.NewFile()
	sheet := "Sheet1"

	headers := []string{"ID кейса", "Дата и время создания", "ИИН", "ФИО", "Тип персонального данных", "Номер телефона", "Банк", "Лицевой счет"}
	columns := []string{"A", "B", "C", "D", "E", "F", "G", "H", "I"}

	for i, header := range headers {
		f.SetCellValue(sheet, fmt.Sprintf("%s1", columns[i]), header)
	}

	for idx, smp := range smpResult {
		row := idx + 2
		f.SetCellValue(sheet, fmt.Sprintf("A%d", row), smp.EventID)
		f.SetCellValue(sheet, fmt.Sprintf("B%d", row), smp.OperDateTime)
		f.SetCellValue(sheet, fmt.Sprintf("C%d", row), smp.IIN)
		f.SetCellValue(sheet, fmt.Sprintf("D%d", row), smp.FullName)
		f.SetCellValue(sheet, fmt.Sprintf("E%d", row), smp.PersonType)
		f.SetCellValue(sheet, fmt.Sprintf("F%d", row), smp.MobileNumber)
		f.SetCellValue(sheet, fmt.Sprintf("G%d", row), smp.BankName)
		f.SetCellValue(sheet, fmt.Sprintf("H%d", row), smp.LicAccount)
	}

	for _, col := range columns {
		f.SetColWidth(sheet, col, col, 20)
	}

	filename := "smp.xlsx"
	if !startDate.IsZero() && !endDate.IsZero() {
		filename = fmt.Sprintf("smp %s - %s.xlsx", startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
	}

	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	if err := f.Write(c.Writer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to write file"})
	}
}
