package handlers

import (
	"admin-panel/db"
	"admin-panel/middleware"
	"admin-panel/models"
	"errors"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	tool "projects.post.kz/antifraud-online/tool"
)

// GetScenarios godoc
// @Summary Получение списка сценариев
// @Tags scenarios
// @Produce json
// @Param page query string false "page"
// @Param per_page query string false "per_page"
// @Param sort query string false "Сортировка asc/desc" Enums(asc, desc)
// @Param sort_field query string false "Поля для сортировки через запятую"
// @Param filter_values query string false "Значение поля для фильтрации, через запятую (в паре с filter_field)"
// @Param filter_fields query string false "Название полей для фильтрации, через запятую (в паре с filter_value)"
// @Param filter_type query string false "Тип фильтра полное совпадение (=) или совпадение по like" Enums(=, like)
// @Param filter_logic query string false "Пересечение или дополнение" Enums(and, or)
// @Success 200 {object} models.Scenario
// @Router /scenario/list [get]
func GetScenarios(c *gin.Context) {

	result, err := tool.GetListGin(models.Scenario{}, c, db.DBConn, nil)

	if errors.Is(err, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, err))
		return
	}

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(result, nil))
	return
}

// UpdateScenario godoc
// @Summary Изменение сценария
// @Tags scenarios
// @Produce json
// @Param Req body models.Scenario true "Add request"
// @Success 200 {object} models.Scenario
// @Router /scenario [put]
func UpdateScenario(c *gin.Context) {
	err := middleware.VerifyToken(c, models.RoleAdmin)
	if err != nil {
		c.JSON(403, response(nil, err))
		return
	}

	var cs models.Scenario

	err = c.BindJSON(&cs)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	err = db.DBConn.Save(&cs).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(cs, nil))
	return
}
