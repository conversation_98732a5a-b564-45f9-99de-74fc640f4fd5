package handlers

import (
	"admin-panel/db"
	"admin-panel/services"
	"errors"
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	tool "projects.post.kz/antifraud-online/tool"
)

// GetEvents godoc
// @Summary Получение списка евентов
// @Tags events
// @Produce json
// @Param page query string false "page"
// @Param per_page query string false "per_page"
// @Param sort query string false "Сортировка asc/desc" Enums(asc, desc)
// @Param sort_field query string false "Поля для сортировки через запятую"
// @Param filter_values query string false "Значение поля для фильтрации, через запятую (в паре с filter_field)"
// @Param filter_fields query string false "Название полей для фильтрации, через запятую (в паре с filter_value)"
// @Param filter_type query string false "Тип фильтра полное совпадение (=) или совпадение по like" Enums(=, like)
// @Param filter_logic query string false "Пересечение или дополнение" Enums(and, or)
// @Success 200 {object} services.Event
// @Router /event/list [get]
func GetEvents(c *gin.Context) {

	result, err := tool.GetListGin(services.Event{}, c, db.DBConn, []string{"ChangeProfileEvent", "FinOper", "FinOper.FinoperDc", "FinOper.Person"})

	if errors.Is(err, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, err))
		return
	}

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(result, nil))
	return
}

// GetEventByCaseId godoc
// @Summary Получение евента по id case
// @Tags events
// @Produce json
// @Param case path string true "case id"
// @Success 200 {object} services.Event
// @Router /event/case/{case} [get]
func GetEventByCaseId(c *gin.Context) {

	id, err := strconv.Atoi(c.Param("case"))
	if err != nil {
		c.JSON(400, response(nil, fmt.Errorf("Invalid case id")))
		return
	}

	list, err := services.GetEventByCaseId(id)

	if errors.Is(err, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, err))
		return
	}

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(list, nil))
	return
}
