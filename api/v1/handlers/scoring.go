package handlers

import (
	"admin-panel/db"
	"admin-panel/models"
	"admin-panel/services"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	tool "projects.post.kz/antifraud-online/tool"
)

// GetScoringParams godoc
// @Summary Получение параметров скоринга
// @Tags scoring
// @Produce json
// @Param page query string false "page"
// @Param per_page query string false "per_page"
// @Param sort query string false "Сортировка asc/desc" Enums(asc, desc)
// @Param sort_field query string false "Поля для сортировки через запятую"
// @Param filter_values query string false "Значение поля для фильтрации, через запятую (в паре с filter_field)"
// @Param filter_fields query string false "Название полей для фильтрации, через запятую (в паре с filter_value)"
// @Param filter_type query string false "Тип фильтра полное совпадение (=) или совпадение по like" Enums(=, like)
// @Param filter_logic query string false "Пересечение или дополнение" Enums(and, or)
// @Success 200 {object} models.Scoring
// @Router /scoring/list [get]
func GetScoringParams(c *gin.Context) {

	result, err := tool.GetListGin(models.Scoring{}, c, db.DBConn, nil)

	if errors.Is(err, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, err))
		return
	}

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(result, nil))
	return
}

// UpdateScoringParams godoc
// @Summary Изменение параметров скоринга
// @Tags scoring
// @Produce json
// @Param Req body models.Scoring true "Add request"
// @Success 200 {object} models.Scoring
// @Router /scoring [put]
func UpdateScoringParams(c *gin.Context) {
	var (
		cs      models.Scoring
		isExist bool
	)

	err := c.BindJSON(&cs)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	if cs.Id < 1 {
		c.JSON(400, response(nil, fmt.Errorf("Invalid param Id")))
		return
	}

	if cs.SystemId < 1 {
		c.JSON(400, response(nil, fmt.Errorf("Invalid System Id")))
		return
	}

	if cs.EventTypeId < 1 {
		c.JSON(400, response(nil, fmt.Errorf("Invalid Event Type Id")))
		return
	}

	if cs.MinScore >= cs.MaxScore {
		c.JSON(400, response(nil, fmt.Errorf("Min score cannot be more than Max score ")))
		return
	}

	_, list, err := services.GetSpr("event_status")

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	//Чекаем есть ли такой статус в справочнике
	for _, val := range list {
		if val.Key == strconv.Itoa(cs.DecisionStatus) {
			isExist = true
			break
		}
	}

	if !isExist {
		c.JSON(400, response(nil, fmt.Errorf("Invalid Decision status")))
		return
	}

	cs.RiskLevel = strings.Title(strings.ToLower(cs.RiskLevel))
	cs.RiskLevelKz = strings.Title(strings.ToLower(cs.RiskLevelKz))

	err = db.DBConn.Save(&cs).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(cs, nil))
	return
}

// AddScoringParams godoc
// @Summary Добавление значения парметров скоринга
// @Tags scoring
// @Produce json
// @Param Req body models.Scoring true "Add request"
// @Success 200 {object} models.Scoring
// @Router /scoring [post]
func AddScoringParams(c *gin.Context) {
	var (
		cs      models.Scoring
		isExist bool
	)

	err := c.BindJSON(&cs)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	if cs.SystemId < 1 {
		c.JSON(400, response(nil, fmt.Errorf("Invalid System Id")))
		return
	}

	if cs.EventTypeId < 1 {
		c.JSON(400, response(nil, fmt.Errorf("Invalid Event Type Id")))
		return
	}

	if cs.MinScore >= cs.MaxScore {
		c.JSON(400, response(nil, fmt.Errorf("Min score cannot be more than Max score ")))
		return
	}

	_, list, err := services.GetSpr("event_status")

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	//Чекаем есть ли такой статус в справочнике
	for _, val := range list {
		if val.Key == strconv.Itoa(cs.DecisionStatus) {
			isExist = true
			break
		}
	}

	if !isExist {
		c.JSON(400, response(nil, fmt.Errorf("Invalid Decision status")))
		return
	}

	cs.RiskLevel = strings.Title(strings.ToLower(cs.RiskLevel))

	err = db.DBConn.Create(&cs).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(cs, nil))
	return
}

// DelScoringParams godoc
// @Summary Удаление параметров скоринга
// @Tags scoring
// @Produce json
// @Param code path string true "code"
// @Success 200 {object} models.Scoring
// @Router /scoring/{id} [delete]
func DelScoringParams(c *gin.Context) {
	id := c.Param("id")

	err := db.DBConn.Where("id = ?", id).Delete(&models.Scoring{}).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(nil, nil))
	return
}
