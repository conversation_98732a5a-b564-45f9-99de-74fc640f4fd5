package handlers

import (
	"admin-panel/db"
	"admin-panel/models"
	"admin-panel/services"
	"errors"
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	tool "projects.post.kz/antifraud-online/tool"
)

// GetProducts godoc
// @Summary Получение справочника продуктов
// @Tags hdbk
// @Produce json
// @Param page query string false "page"
// @Param per_page query string false "per_page"
// @Param sort query string false "Сортировка asc/desc" Enums(asc, desc)
// @Param sort_field query string false "Поля для сортировки через запятую"
// @Param filter_values query string false "Значение поля для фильтрации, через запятую (в паре с filter_field)"
// @Param filter_fields query string false "Название полей для фильтрации, через запятую (в паре с filter_value)"
// @Param filter_type query string false "Тип фильтра полное совпадение (=) или совпадение по like" Enums(=, like)
// @Param filter_logic query string false "Пересечение или дополнение" Enums(and, or)
// @Success 200 {object} models.Product
// @Router /hdbk/product/list [get]
func GetProducts(c *gin.Context) {

	result, err := tool.GetListGin(models.Product{}, c, db.DBConn, nil)

	if errors.Is(err, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, err))
		return
	}

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(result, nil))
	return
}

// UpdateProduct godoc
// @Summary Изменение значения в справочнике продуктов
// @Tags hdbk
// @Produce json
// @Param Req body models.Product true "Add request"
// @Success 200 {object} models.Product
// @Router /hdbk/product [put]
func UpdateProduct(c *gin.Context) {
	var p models.Product

	err := c.BindJSON(&p)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	err = db.DBConn.Save(&p).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(p, nil))
	return
}

// AddProduct godoc
// @Summary Добавление значения в справочнике продуктов
// @Tags hdbk
// @Produce json
// @Param Req body models.Product true "Add request"
// @Success 200 {object} models.Product
// @Router /hdbk/product [post]
func AddProduct(c *gin.Context) {
	var p models.Product

	err := c.BindJSON(&p)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	err = db.DBConn.Create(&p).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(p, nil))
	return
}

// DeleteProduct godoc
// @Summary Удаление продукта из справочника по code
// @Tags hdbk
// @Produce json
// @Success 200 {object} models.Product
// @Router /hdbk/product/{code} [delete]
func DeleteProduct(c *gin.Context) {
	code := c.Param("code")

	err := db.DBConn.Where("code = ?", code).Delete(&models.Product{}).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(nil, nil))
	return
}

// GetCaseButtons godoc
// @Summary Получение кнопок обработки кейса
// @Tags hdbk
// @Produce json
// @Param page query string false "page"
// @Param per_page query string false "per_page"
// @Success 200 {object} models.Spr
// @Router /hdbk/case_buttons [get]
func GetCaseButtons(c *gin.Context) {
	var (
		list []models.Spr
		err  error
		cnt  int64
	)

	paginResult := tool.SetPaginationGin(c)

	cnt, list, err = services.GetSpr("admin_result_buttons")

	if errors.Is(err, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, err))
		return
	}

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	paginResult.Items = list
	paginResult.Count = cnt

	c.JSON(200, response(paginResult, nil))
}

// UpdateCaseButton godoc
// @Summary Изменение кнопок обработки кейса
// @Tags hdbk
// @Produce json
// @Param Req body models.Spr true "Add request"
// @Success 200 {object} models.Spr
// @Router /hdbk/case_buttons [put]
func UpdateCaseButton(c *gin.Context) {
	var (
		row     models.Spr
		tblName string = models.Spr{}.TableName()
	)

	err := c.BindJSON(&row)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	row.Name = "admin_result_buttons"
	err = db.DBConn.Table(tblName).Where("name = ? and key = ?", row.Name, row.Key).Update("disabled", row.Disabled).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(row, nil))
	return
}

// GetEventStatuses godoc
// @Summary Получение справочника статусов
// @Tags hdbk
// @Produce json
// @Param page query string false "page"
// @Param per_page query string false "per_page"
// @Success 200 {object} models.Spr
// @Router /hdbk/event_statuses [get]
func GetEventStatuses(c *gin.Context) {
	var (
		list []models.Spr
		err  error
		cnt  int64
	)

	paginResult := tool.SetPaginationGin(c)

	cnt, list, err = services.GetSpr("event_status")

	if errors.Is(err, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, err))
		return
	}

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	paginResult.Items = list
	paginResult.Count = cnt

	c.JSON(200, response(paginResult, nil))
}

// GetFinServices godoc
// @Summary Получение справочника фин сервисов
// @Tags hdbk
// @Produce json
// @Param page query string false "page"
// @Param per_page query string false "per_page"
// @Param sort query string false "Сортировка asc/desc" Enums(asc, desc)
// @Param sort_field query string false "Поля для сортировки через запятую"
// @Param filter_values query string false "Значение поля для фильтрации, через запятую (в паре с filter_field)"
// @Param filter_fields query string false "Название полей для фильтрации, через запятую (в паре с filter_value)"
// @Param filter_type query string false "Тип фильтра полное совпадение (=) или совпадение по like" Enums(=, like)
// @Param filter_logic query string false "Пересечение или дополнение" Enums(and, or)
// @Success 200 {object} models.FinService
// @Router /hdbk/fin-services/list [get]
func GetFinServices(c *gin.Context) {

	result, err := tool.GetListGin(models.FinService{}, c, db.DBConn, nil)

	if errors.Is(err, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, err))
		return
	}

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(result, nil))
	return
}

// UpdateFinService godoc
// @Summary Изменение значения в справочнике фин сервисов
// @Tags hdbk
// @Produce json
// @Param Req body models.FinService true "Add request"
// @Success 200 {object} models.FinService
// @Router /hdbk/fin-services [put]
func UpdateFinService(c *gin.Context) {
	var p models.FinService

	err := c.BindJSON(&p)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	err = db.DBConn.Save(&p).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(p, nil))
	return
}

// AddFinService godoc
// @Summary Добавление значения в справочнике фин сервисов
// @Tags hdbk
// @Produce json
// @Param Req body models.FinService true "Add request"
// @Success 200 {object} models.FinService
// @Router /hdbk/fin-services [post]
func AddFinService(c *gin.Context) {
	var p models.FinService

	err := c.BindJSON(&p)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	err = db.DBConn.Create(&p).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(p, nil))
	return
}

// DeleteFinService godoc
// @Summary Удаление продукта из справочника по code
// @Tags hdbk
// @Produce json
// @Param code path string true "code"
// @Success 200 {object} models.FinService
// @Router /hdbk/fin-services/{code} [delete]
func DeleteFinService(c *gin.Context) {
	code := c.Param("code")

	err := db.DBConn.Where("code = ?", code).Delete(&models.FinService{}).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(nil, nil))
	return
}

// GetSystems godoc
// @Summary Получение справочника систем
// @Tags hdbk
// @Produce json
// @Param page query string false "page"
// @Param per_page query string false "per_page"
// @Param sort query string false "Сортировка asc/desc" Enums(asc, desc)
// @Param sort_field query string false "Поля для сортировки через запятую"
// @Param filter_values query string false "Значение поля для фильтрации, через запятую (в паре с filter_field)"
// @Param filter_fields query string false "Название полей для фильтрации, через запятую (в паре с filter_value)"
// @Param filter_type query string false "Тип фильтра полное совпадение (=) или совпадение по like" Enums(=, like)
// @Param filter_logic query string false "Пересечение или дополнение" Enums(and, or)
// @Success 200 {object} models.System
// @Router /hdbk/system/list [get]
func GetSystems(c *gin.Context) {

	result, err := tool.GetListGin(models.System{}, c, db.DBConn, nil)

	if errors.Is(err, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, err))
		return
	}

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(result, nil))
	return
}

// UpdateSystem godoc
// @Summary Изменение значения в справочнике систем
// @Tags hdbk
// @Produce json
// @Param Req body models.FinService true "Add request"
// @Success 200 {object} models.System
// @Router /hdbk/system [put]
func UpdateSystem(c *gin.Context) {
	var p models.System

	err := c.BindJSON(&p)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	err = db.DBConn.Save(&p).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(p, nil))
	return
}

// AddSystem godoc
// @Summary Добавление значения в справочнике систем
// @Tags hdbk
// @Produce json
// @Param Req body models.System true "Add request"
// @Success 200 {object} models.System
// @Router /hdbk/system [post]
func AddSystem(c *gin.Context) {
	var p models.System

	err := c.BindJSON(&p)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	err = db.DBConn.Create(&p).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(p, nil))
	return
}

// DeleteSystem godoc
// @Summary Удаление значения из справочника по id
// @Tags hdbk
// @Produce json
// @Param id path string true "id"
// @Success 200 {object} models.System
// @Router /hdbk/system/{id} [delete]
func DeleteSystem(c *gin.Context) {
	id := c.Param("id")

	i, err := strconv.Atoi(id)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	err = db.DBConn.Where("id = ?", i).Delete(&models.System{}).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(nil, nil))
	return
}

// GetEventStatuses godoc
// @Summary Получение справочника типов событий
// @Tags hdbk
// @Produce json
// @Param page query string false "page"
// @Param per_page query string false "per_page"
// @Success 200 {object} models.EventType
// @Router /hdbk/event_types/list [get]
func GetEventTypes(c *gin.Context) {
	result, err := tool.GetListGin(models.EventType{}, c, db.DBConn, nil)

	if errors.Is(err, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, err))
		return
	}

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(result, nil))
	return
}

// GetHistorySettings godoc
// @Summary Получение настроек хранения данных
// @Tags hdbk
// @Produce json
// @Param page query string false "page"
// @Param per_page query string false "per_page"
// @Success 200 {object} models.Spr
// @Router /hdbk/history/list [get]
func GetHistorySettings(c *gin.Context) {
	var (
		list []models.Spr
		err  error
		cnt  int64
	)

	paginResult := tool.SetPaginationGin(c)

	cnt, list, err = services.GetSpr("history-interval")

	if errors.Is(err, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, err))
		return
	}

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	paginResult.Items = list
	paginResult.Count = cnt

	c.JSON(200, response(paginResult, nil))
}

// UpdateHistorySettings godoc
// @Summary Изменение настроек хранения данных
// @Tags hdbk
// @Produce json
// @Param Req body models.Spr true "Add request"
// @Success 200 {object} models.Spr
// @Router /hdbk/history [put]
func UpdateHistorySettings(c *gin.Context) {
	var (
		row     models.Spr
		tblName string = models.Spr{}.TableName()
	)

	err := c.BindJSON(&row)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	if services.ValidPeriod(row.Value) == false {
		c.JSON(400, response(nil, fmt.Errorf("Invalid interval")))
		return
	}

	row.Name = "history-interval"
	err = db.DBConn.Table(tblName).Where("name = ? and key = ?", row.Name, row.Key).Save(&row).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(row, nil))
	return
}

// GetSolutionSettings godoc
// @Summary Получение настроек по песочнице
// @Tags hdbk
// @Produce json
// @Param page query string false "page"
// @Param per_page query string false "per_page"
// @Success 200 {object} models.Spr
// @Router /hdbk/solution/list [get]
func GetSolutionSettings(c *gin.Context) {
	var (
		list []models.Spr
		err  error
		cnt  int64
	)

	paginResult := tool.SetPaginationGin(c)

	cnt, list, err = services.GetSpr("solution-settings")

	if errors.Is(err, gorm.ErrRecordNotFound) {
		c.JSON(404, response(nil, err))
		return
	}

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	paginResult.Items = list
	paginResult.Count = cnt

	c.JSON(200, response(paginResult, nil))
}

// UpdateSolutionSettings godoc
// @Summary Изменение настроек по песочнице
// @Tags hdbk
// @Produce json
// @Param Req body models.Spr true "Add request"
// @Success 200 {object} models.Spr
// @Router /hdbk/solution [put]
func UpdateSolutionSettings(c *gin.Context) {
	var (
		row     models.Spr
		tblName string = models.Spr{}.TableName()
	)

	err := c.BindJSON(&row)

	if err != nil {
		c.JSON(503, response(nil, err))
		return
	}

	if val, err := strconv.ParseBool(row.Value); err != nil {
		c.JSON(400, response(nil, fmt.Errorf("Invalid param: %v", val)))
		return
	}

	row.Name = "solution-settings"
	err = db.DBConn.Table(tblName).Where("name = ? and key = ?", row.Name, row.Key).Save(&row).Error

	if err != nil {
		c.JSON(400, response(nil, err))
		return
	}

	c.JSON(200, response(row, nil))
	return
}
