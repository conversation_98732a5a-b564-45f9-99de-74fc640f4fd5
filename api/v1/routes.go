package v1

import (
	"admin-panel/api/v1/handlers"

	"admin-panel/middleware"
	"admin-panel/models"

	"github.com/gin-gonic/gin"
)

func Routes(router *gin.Engine) {

	v1 := router.Group("/admin-panel/api/v1/")
	{
		v1.Use(middleware.AuthMiddleware(models.RoleAdmin))
		cardBlackList := v1.Group("card-black-list")
		{
			cardBlackList.POST("", handlers.CreateCardBlackList)
			cardBlackList.GET("list", handlers.GetListCardBlackList)
			cardBlackList.PUT("", handlers.UpdateCardBlackList)
			cardBlackList.DELETE("", handlers.DeleteCardBlackList)
		}
		cases := v1.Group("case")
		{
			cases.GET("list", handlers.GetCases)
			cases.GET("cases-detailed", handlers.CasesDetailed)
			cases.GET("export", handlers.ExportCasesToExcel)
			cases.PUT("", handlers.UpdateCase)
			cases.PATCH("/assign", handlers.AssignedTo)

			comment := cases.Group("comment")
			{
				comment.GET("/:id", handlers.GetCaseComments)
				comment.POST("", handlers.AddCaseComment)
				comment.PUT("", handlers.UpdateCaseComment)
				comment.DELETE("/:id", handlers.DelCaseComment)
			}
		}
		smp := v1.Group("smp")
		{
			smp.GET("list", handlers.GetSMP)
			smp.GET("export", handlers.ExportSMPToExcel)

		}

		pensioner_opers := v1.Group("pensioner-opers")
		{
			pensioner_opers.GET("list", handlers.GetPensionerOperations)
			pensioner_opers.GET("export", handlers.ExportPensionerOperationsToExcel)

		}
		RelatedTransfers := v1.Group("related-transfers")
		{
			RelatedTransfers.POST("list", handlers.GetRelatedTransfers)
		}
		moneyTransfers := v1.Group("money_transfers")
		{
			moneyTransfers.GET("list", handlers.GetMoneyTransfers)
			moneyTransfers.GET("export", handlers.ExportMoneyTransfersToExcel)
		}

		events := v1.Group("event")
		{
			events.GET("list", handlers.GetEvents)
			events.GET("case/:case", handlers.GetEventByCaseId)
		}

		scenario := v1.Group("scenario")
		{
			scenario.GET("list", handlers.GetScenarios)
			scenario.PUT("", handlers.UpdateScenario)
		}

		scoring := v1.Group("scoring")
		{
			scoring.GET("list", handlers.GetScoringParams)
			scoring.PUT("", handlers.UpdateScoringParams)
			scoring.POST("", handlers.AddScoringParams)
			scoring.DELETE("/:id", handlers.DelScoringParams)
		}

		hdbk := v1.Group("hdbk")
		{
			products := hdbk.Group("product")
			{
				products.GET("list", handlers.GetProducts)
				products.PUT("", handlers.UpdateProduct)
				products.POST("", handlers.AddProduct)
				products.DELETE("/:code", handlers.DeleteProduct)
			}

			finServices := hdbk.Group("fin-services")
			{
				finServices.GET("list", handlers.GetFinServices)
				finServices.PUT("", handlers.UpdateFinService)
				finServices.POST("", handlers.AddFinService)
				finServices.DELETE("/:code", handlers.DeleteFinService)
			}

			system := hdbk.Group("system")
			{
				system.GET("list", handlers.GetSystems)
				system.PUT("", handlers.UpdateSystem)
				system.POST("", handlers.AddSystem)
				system.DELETE("/:id", handlers.DeleteSystem)
			}

			caseButtons := hdbk.Group("case_buttons")
			{
				caseButtons.GET("", handlers.GetCaseButtons)
				caseButtons.PUT("", handlers.UpdateCaseButton)
			}
			eventStatuses := hdbk.Group("event_statuses")
			{
				eventStatuses.GET("", handlers.GetEventStatuses)
			}
			eventTypes := hdbk.Group("event_types")
			{
				eventTypes.GET("list", handlers.GetEventTypes)
			}

			history := hdbk.Group("history")
			{
				history.GET("list", handlers.GetHistorySettings)
				history.PUT("", handlers.UpdateHistorySettings)
			}

			solution := hdbk.Group("solution")
			{
				solution.GET("list", handlers.GetSolutionSettings)
				solution.PUT("", handlers.UpdateSolutionSettings)
			}
		}

	}
}
