// Package docs GENERATED BY THE COMMAND ABOVE; DO NOT EDIT
// This file was generated by swaggo/swag
package docs

import (
	"bytes"
	"encoding/json"
	"strings"
	"text/template"

	"github.com/swaggo/swag"
)

var doc = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/case": {
            "put": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "cases"
                ],
                "summary": "Изменение кейса",
                "parameters": [
                    {
                        "description": "Add request",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Case"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Case"
                        }
                    }
                }
            }
        },
        "/case/assign": {
            "patch": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "cases"
                ],
                "summary": "Переназначить кейс",
                "parameters": [
                    {
                        "description": "Add request",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Case"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Case"
                        }
                    }
                }
            }
        },
        "/case/comment": {
            "put": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "cases"
                ],
                "summary": "Изменение коммента к кейсу",
                "parameters": [
                    {
                        "description": "Add request",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CaseComment"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/CaseComment"
                        }
                    }
                }
            },
            "post": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "cases"
                ],
                "summary": "Добавление коммента к кейсу",
                "parameters": [
                    {
                        "description": "Add request",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CaseComment"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/CaseComment"
                        }
                    }
                }
            }
        },
        "/case/comment/{id}": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "cases"
                ],
                "summary": "Получение списка комментов кейсов",
                "parameters": [
                    {
                        "type": "string",
                        "description": "case id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/CaseComment"
                        }
                    }
                }
            },
            "delete": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "cases"
                ],
                "summary": "Добавление коммента к кейсу",
                "parameters": [
                    {
                        "type": "string",
                        "description": "comment id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/CaseComment"
                        }
                    }
                }
            }
        },
        "/case/list": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "cases"
                ],
                "summary": "Получение списка кейсов",
                "parameters": [
                    {
                        "type": "string",
                        "description": "page",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "per_page",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "description": "Сортировка asc/desc",
                        "name": "sort",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Поля для сортировки через запятую",
                        "name": "sort_field",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Значение поля для фильтрации, через запятую (в паре с filter_field)",
                        "name": "filter_values",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Название полей для фильтрации, через запятую (в паре с filter_value)",
                        "name": "filter_fields",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "=",
                            "like"
                        ],
                        "type": "string",
                        "description": "Тип фильтра полное совпадение (=) или совпадение по like",
                        "name": "filter_type",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "and",
                            "or"
                        ],
                        "type": "string",
                        "description": "Пересечение или дополнение",
                        "name": "filter_logic",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/CaseExternal"
                        }
                    }
                }
            }
        },
        "/event/case/{case}": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "events"
                ],
                "summary": "Получение евента по id case",
                "parameters": [
                    {
                        "type": "string",
                        "description": "case id",
                        "name": "case",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Event"
                        }
                    }
                }
            }
        },
        "/event/list": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "events"
                ],
                "summary": "Получение списка евентов",
                "parameters": [
                    {
                        "type": "string",
                        "description": "page",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "per_page",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "description": "Сортировка asc/desc",
                        "name": "sort",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Поля для сортировки через запятую",
                        "name": "sort_field",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Значение поля для фильтрации, через запятую (в паре с filter_field)",
                        "name": "filter_values",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Название полей для фильтрации, через запятую (в паре с filter_value)",
                        "name": "filter_fields",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "=",
                            "like"
                        ],
                        "type": "string",
                        "description": "Тип фильтра полное совпадение (=) или совпадение по like",
                        "name": "filter_type",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "and",
                            "or"
                        ],
                        "type": "string",
                        "description": "Пересечение или дополнение",
                        "name": "filter_logic",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Event"
                        }
                    }
                }
            }
        },
        "/hdbk/case_buttons": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Получение кнопок обработки кейса",
                "parameters": [
                    {
                        "type": "string",
                        "description": "page",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "per_page",
                        "name": "per_page",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Spr"
                        }
                    }
                }
            },
            "put": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Изменение кнопок обработки кейса",
                "parameters": [
                    {
                        "description": "Add request",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Spr"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Spr"
                        }
                    }
                }
            }
        },
        "/hdbk/event_statuses": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Получение справочника статусов",
                "parameters": [
                    {
                        "type": "string",
                        "description": "page",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "per_page",
                        "name": "per_page",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Spr"
                        }
                    }
                }
            }
        },
        "/hdbk/event_types/list": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Получение справочника типов событий",
                "parameters": [
                    {
                        "type": "string",
                        "description": "page",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "per_page",
                        "name": "per_page",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/EventType"
                        }
                    }
                }
            }
        },
        "/hdbk/fin-services": {
            "put": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Изменение значения в справочнике фин сервисов",
                "parameters": [
                    {
                        "description": "Add request",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/FinService"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/FinService"
                        }
                    }
                }
            },
            "post": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Добавление значения в справочнике фин сервисов",
                "parameters": [
                    {
                        "description": "Add request",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/FinService"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/FinService"
                        }
                    }
                }
            }
        },
        "/hdbk/fin-services/list": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Получение справочника фин сервисов",
                "parameters": [
                    {
                        "type": "string",
                        "description": "page",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "per_page",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "description": "Сортировка asc/desc",
                        "name": "sort",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Поля для сортировки через запятую",
                        "name": "sort_field",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Значение поля для фильтрации, через запятую (в паре с filter_field)",
                        "name": "filter_values",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Название полей для фильтрации, через запятую (в паре с filter_value)",
                        "name": "filter_fields",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "=",
                            "like"
                        ],
                        "type": "string",
                        "description": "Тип фильтра полное совпадение (=) или совпадение по like",
                        "name": "filter_type",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "and",
                            "or"
                        ],
                        "type": "string",
                        "description": "Пересечение или дополнение",
                        "name": "filter_logic",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/FinService"
                        }
                    }
                }
            }
        },
        "/hdbk/fin-services/{code}": {
            "delete": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Удаление продукта из справочника по code",
                "parameters": [
                    {
                        "type": "string",
                        "description": "code",
                        "name": "code",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/FinService"
                        }
                    }
                }
            }
        },
        "/hdbk/history": {
            "put": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Изменение настроек хранения данных",
                "parameters": [
                    {
                        "description": "Add request",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Spr"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Spr"
                        }
                    }
                }
            }
        },
        "/hdbk/history/list": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Получение настроек хранения данных",
                "parameters": [
                    {
                        "type": "string",
                        "description": "page",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "per_page",
                        "name": "per_page",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Spr"
                        }
                    }
                }
            }
        },
        "/hdbk/product": {
            "put": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Изменение значения в справочнике продуктов",
                "parameters": [
                    {
                        "description": "Add request",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Product"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Product"
                        }
                    }
                }
            },
            "post": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Добавление значения в справочнике продуктов",
                "parameters": [
                    {
                        "description": "Add request",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Product"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Product"
                        }
                    }
                }
            }
        },
        "/hdbk/product/list": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Получение справочника продуктов",
                "parameters": [
                    {
                        "type": "string",
                        "description": "page",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "per_page",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "description": "Сортировка asc/desc",
                        "name": "sort",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Поля для сортировки через запятую",
                        "name": "sort_field",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Значение поля для фильтрации, через запятую (в паре с filter_field)",
                        "name": "filter_values",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Название полей для фильтрации, через запятую (в паре с filter_value)",
                        "name": "filter_fields",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "=",
                            "like"
                        ],
                        "type": "string",
                        "description": "Тип фильтра полное совпадение (=) или совпадение по like",
                        "name": "filter_type",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "and",
                            "or"
                        ],
                        "type": "string",
                        "description": "Пересечение или дополнение",
                        "name": "filter_logic",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Product"
                        }
                    }
                }
            }
        },
        "/hdbk/product/{code}": {
            "delete": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Удаление продукта из справочника по code",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Product"
                        }
                    }
                }
            }
        },
        "/hdbk/solution": {
            "put": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Изменение настроек по песочнице",
                "parameters": [
                    {
                        "description": "Add request",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Spr"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Spr"
                        }
                    }
                }
            }
        },
        "/hdbk/solution/list": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Получение настроек по песочнице",
                "parameters": [
                    {
                        "type": "string",
                        "description": "page",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "per_page",
                        "name": "per_page",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Spr"
                        }
                    }
                }
            }
        },
        "/hdbk/system": {
            "put": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Изменение значения в справочнике систем",
                "parameters": [
                    {
                        "description": "Add request",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/FinService"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/System"
                        }
                    }
                }
            },
            "post": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Добавление значения в справочнике систем",
                "parameters": [
                    {
                        "description": "Add request",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/System"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/System"
                        }
                    }
                }
            }
        },
        "/hdbk/system/list": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Получение справочника систем",
                "parameters": [
                    {
                        "type": "string",
                        "description": "page",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "per_page",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "description": "Сортировка asc/desc",
                        "name": "sort",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Поля для сортировки через запятую",
                        "name": "sort_field",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Значение поля для фильтрации, через запятую (в паре с filter_field)",
                        "name": "filter_values",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Название полей для фильтрации, через запятую (в паре с filter_value)",
                        "name": "filter_fields",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "=",
                            "like"
                        ],
                        "type": "string",
                        "description": "Тип фильтра полное совпадение (=) или совпадение по like",
                        "name": "filter_type",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "and",
                            "or"
                        ],
                        "type": "string",
                        "description": "Пересечение или дополнение",
                        "name": "filter_logic",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/System"
                        }
                    }
                }
            }
        },
        "/hdbk/system/{id}": {
            "delete": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "hdbk"
                ],
                "summary": "Удаление значения из справочника по id",
                "parameters": [
                    {
                        "type": "string",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/System"
                        }
                    }
                }
            }
        },
        "/scenario": {
            "put": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "scenarios"
                ],
                "summary": "Изменение сценария",
                "parameters": [
                    {
                        "description": "Add request",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Scenario"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Scenario"
                        }
                    }
                }
            }
        },
        "/scenario/list": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "scenarios"
                ],
                "summary": "Получение списка сценариев",
                "parameters": [
                    {
                        "type": "string",
                        "description": "page",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "per_page",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "description": "Сортировка asc/desc",
                        "name": "sort",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Поля для сортировки через запятую",
                        "name": "sort_field",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Значение поля для фильтрации, через запятую (в паре с filter_field)",
                        "name": "filter_values",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Название полей для фильтрации, через запятую (в паре с filter_value)",
                        "name": "filter_fields",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "=",
                            "like"
                        ],
                        "type": "string",
                        "description": "Тип фильтра полное совпадение (=) или совпадение по like",
                        "name": "filter_type",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "and",
                            "or"
                        ],
                        "type": "string",
                        "description": "Пересечение или дополнение",
                        "name": "filter_logic",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Scenario"
                        }
                    }
                }
            }
        },
        "/scoring": {
            "put": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "scoring"
                ],
                "summary": "Изменение параметров скоринга",
                "parameters": [
                    {
                        "description": "Add request",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Scoring"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Scoring"
                        }
                    }
                }
            },
            "post": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "scoring"
                ],
                "summary": "Добавление значения парметров скоринга",
                "parameters": [
                    {
                        "description": "Add request",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/Scoring"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Scoring"
                        }
                    }
                }
            }
        },
        "/scoring/list": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "scoring"
                ],
                "summary": "Получение параметров скоринга",
                "parameters": [
                    {
                        "type": "string",
                        "description": "page",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "per_page",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "description": "Сортировка asc/desc",
                        "name": "sort",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Поля для сортировки через запятую",
                        "name": "sort_field",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Значение поля для фильтрации, через запятую (в паре с filter_field)",
                        "name": "filter_values",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Название полей для фильтрации, через запятую (в паре с filter_value)",
                        "name": "filter_fields",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "=",
                            "like"
                        ],
                        "type": "string",
                        "description": "Тип фильтра полное совпадение (=) или совпадение по like",
                        "name": "filter_type",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "and",
                            "or"
                        ],
                        "type": "string",
                        "description": "Пересечение или дополнение",
                        "name": "filter_logic",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Scoring"
                        }
                    }
                }
            }
        },
        "/scoring/{id}": {
            "delete": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "scoring"
                ],
                "summary": "Удаление параметров скоринга",
                "parameters": [
                    {
                        "type": "string",
                        "description": "code",
                        "name": "code",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Scoring"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "Address": {
            "type": "object",
            "properties": {
                "addr_type": {
                    "type": "string"
                },
                "building": {
                    "type": "string"
                },
                "city": {
                    "type": "string"
                },
                "city_id": {
                    "type": "integer"
                },
                "district": {
                    "type": "string"
                },
                "district_id": {
                    "type": "integer"
                },
                "flat": {
                    "type": "string"
                },
                "old_post_code": {
                    "type": "string"
                },
                "post_code": {
                    "type": "string"
                },
                "region": {
                    "type": "string"
                },
                "region_id": {
                    "type": "integer"
                },
                "rka": {
                    "type": "string"
                },
                "street": {
                    "type": "string"
                }
            }
        },
        "Case": {
            "type": "object",
            "properties": {
                "assigned_user": {
                    "description": "сотрудник, взявшего кейс в работу",
                    "type": "integer"
                },
                "case_details": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/CaseDetails"
                    }
                },
                "case_files": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/CaseFiles"
                    }
                },
                "creation_date": {
                    "description": "Дата и время создания кейса",
                    "type": "string"
                },
                "decision": {
                    "description": "Id статуса обработки кейса",
                    "type": "integer"
                },
                "decision_date": {
                    "description": "Дата и время принятия последнего решения ResponsibleUser",
                    "type": "string"
                },
                "decision_user": {
                    "description": "Идентификатор пользователя (сотрудника), принявшего последнее решение по кейсу",
                    "type": "integer"
                },
                "event_id": {
                    "description": "id из таблицы tb_events",
                    "type": "string"
                },
                "finish_assigned_date": {
                    "description": "Дата и время, срок исполнения",
                    "type": "string"
                },
                "finish_due_date": {
                    "description": "Рассчитанные дата и время, в которые должна быть завершена работа с кейсом - будет настраиваться в разрезе типов кейсов и приоритетов",
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "is_archive": {
                    "description": "Флаг перемещения кейса в архив",
                    "type": "boolean"
                },
                "is_confirmed": {
                    "description": "Флаг устанавливается при подтверждении кейса второй рукой (правило 4-х глаз)",
                    "type": "boolean"
                },
                "priority": {
                    "description": "Приоритет кейса в соответствии с настройками правил",
                    "type": "integer"
                },
                "score": {
                    "description": "Показывает рисковый рейтинг кейса",
                    "type": "integer"
                },
                "start_assigned_date": {
                    "description": "Дата и время, в которое кейс взят в работу",
                    "type": "string"
                },
                "update_date": {
                    "description": "Дата и время последнего редактирования кейса",
                    "type": "string"
                }
            }
        },
        "CaseComment": {
            "type": "object",
            "properties": {
                "case_id": {
                    "description": "id кейса из таблицы tb_cases",
                    "type": "integer"
                },
                "comment": {
                    "description": "Поле отражает последний комментарий о кейсе со стороны пользователей",
                    "type": "string"
                },
                "creation_date": {
                    "description": "Дата и время создания коммента",
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "is_active": {
                    "description": "Флаг деактивация коммента",
                    "type": "boolean"
                },
                "update_date": {
                    "description": "Дата и время последнего редактирования коммента",
                    "type": "string"
                },
                "user_id": {
                    "description": "Идентификатор пользователя (сотрудника)",
                    "type": "integer"
                }
            }
        },
        "CaseDetails": {
            "type": "object",
            "properties": {
                "case_id": {
                    "description": "id кейса из таблицы tb_cases",
                    "type": "integer"
                },
                "case_type": {
                    "description": "Указывает к какому типу относится кейс",
                    "type": "integer"
                },
                "scenario_desc": {
                    "description": "Дополнительное описание при необходимости",
                    "type": "string"
                },
                "scenario_desc_kz": {
                    "description": "Дополнительное описание при необходимости KZ",
                    "type": "string"
                },
                "scenario_id": {
                    "description": "id сценария",
                    "type": "integer"
                },
                "score": {
                    "description": "Показывает рисковый рейтинг кейса",
                    "type": "integer"
                }
            }
        },
        "CaseExternal": {
            "type": "object",
            "properties": {
                "assigned_user": {
                    "description": "сотрудник, взявшего кейс в работу",
                    "type": "integer"
                },
                "case_details": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/CaseDetails"
                    }
                },
                "case_files": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/CaseFiles"
                    }
                },
                "creation_date": {
                    "description": "Дата и время создания кейса",
                    "type": "string"
                },
                "decision": {
                    "description": "Id статуса обработки кейса",
                    "type": "integer"
                },
                "decision_date": {
                    "description": "Дата и время принятия последнего решения ResponsibleUser",
                    "type": "string"
                },
                "decision_user": {
                    "description": "Идентификатор пользователя (сотрудника), принявшего последнее решение по кейсу",
                    "type": "integer"
                },
                "event_id": {
                    "description": "id из таблицы tb_events",
                    "type": "string"
                },
                "event_status": {
                    "type": "integer"
                },
                "finish_assigned_date": {
                    "description": "Дата и время, срок исполнения",
                    "type": "string"
                },
                "finish_due_date": {
                    "description": "Рассчитанные дата и время, в которые должна быть завершена работа с кейсом - будет настраиваться в разрезе типов кейсов и приоритетов",
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "is_archive": {
                    "description": "Флаг перемещения кейса в архив",
                    "type": "boolean"
                },
                "is_confirmed": {
                    "description": "Флаг устанавливается при подтверждении кейса второй рукой (правило 4-х глаз)",
                    "type": "boolean"
                },
                "priority": {
                    "description": "Приоритет кейса в соответствии с настройками правил",
                    "type": "integer"
                },
                "score": {
                    "description": "Показывает рисковый рейтинг кейса",
                    "type": "integer"
                },
                "start_assigned_date": {
                    "description": "Дата и время, в которое кейс взят в работу",
                    "type": "string"
                },
                "update_date": {
                    "description": "Дата и время последнего редактирования кейса",
                    "type": "string"
                }
            }
        },
        "CaseFiles": {
            "type": "object",
            "properties": {
                "case_id": {
                    "description": "id кейса из таблицы tb_cases",
                    "type": "integer"
                },
                "creation_date": {
                    "description": "Дата и время сохранения файла",
                    "type": "string"
                },
                "file_id": {
                    "description": "id файла",
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                }
            }
        },
        "ChangeProfileEvent": {
            "type": "object",
            "properties": {
                "account_number": {
                    "type": "string"
                },
                "address": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Address"
                    }
                },
                "card_exp_date": {
                    "type": "string"
                },
                "card_id": {
                    "type": "integer"
                },
                "card_name": {
                    "type": "string"
                },
                "card_number": {
                    "type": "string"
                },
                "card_type": {
                    "type": "string"
                },
                "limit": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Limit"
                    }
                },
                "password_changed": {
                    "type": "boolean"
                },
                "password_changed_req": {
                    "type": "boolean"
                },
                "pin_changed": {
                    "type": "boolean"
                },
                "pin_reset": {
                    "type": "boolean"
                },
                "push": {
                    "type": "boolean"
                },
                "push_crd": {
                    "type": "boolean"
                },
                "reg_date": {
                    "type": "string"
                },
                "sms_crd": {
                    "type": "boolean"
                },
                "three_ds": {
                    "type": "boolean"
                }
            }
        },
        "Event": {
            "type": "object",
            "properties": {
                "err_code": {
                    "type": "integer"
                },
                "err_info": {
                    "type": "string"
                },
                "event": {
                    "$ref": "#/definitions/IntegrationObject"
                },
                "status": {
                    "type": "integer"
                },
                "status_info": {
                    "type": "string"
                }
            }
        },
        "EventType": {
            "type": "object",
            "properties": {
                "event_type": {
                    "description": "Наименование",
                    "type": "string"
                },
                "event_type_kz": {
                    "description": "Наименование",
                    "type": "string"
                },
                "id": {
                    "description": "код",
                    "type": "integer"
                }
            }
        },
        "FinOper": {
            "type": "object",
            "properties": {
                "branch": {
                    "type": "string"
                },
                "creation_date_time": {
                    "type": "string"
                },
                "finoper_dc": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/FinoperDc"
                    }
                },
                "kbk_code": {
                    "type": "string"
                },
                "kno_code": {
                    "type": "string"
                },
                "knp_code": {
                    "type": "string"
                },
                "oper_date_time": {
                    "type": "string"
                },
                "oper_id": {
                    "type": "string"
                },
                "oper_sts": {
                    "type": "integer"
                },
                "oper_sub_type": {
                    "type": "integer"
                },
                "oper_type": {
                    "type": "integer"
                },
                "paypost_id": {
                    "type": "integer"
                },
                "person": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Person"
                    }
                },
                "post_id": {
                    "type": "integer"
                },
                "product_grp_id": {
                    "type": "integer"
                },
                "product_id": {
                    "type": "string"
                },
                "reason": {
                    "type": "string"
                },
                "ref_oper_id": {
                    "type": "string"
                },
                "service_id": {
                    "type": "string"
                }
            }
        },
        "FinService": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "код продукта",
                    "type": "string"
                },
                "description": {
                    "description": "Описание",
                    "type": "string"
                }
            }
        },
        "FinoperDc": {
            "type": "object",
            "properties": {
                "account_number": {
                    "type": "string"
                },
                "amount": {
                    "type": "number"
                },
                "amount_kzt": {
                    "type": "number"
                },
                "bank_bic": {
                    "type": "string"
                },
                "bank_name": {
                    "type": "string"
                },
                "bs_account": {
                    "type": "string"
                },
                "card_exp_date": {
                    "type": "string"
                },
                "card_id": {
                    "type": "integer"
                },
                "card_name": {
                    "type": "string"
                },
                "card_number": {
                    "type": "string"
                },
                "card_open_date": {
                    "type": "string"
                },
                "card_type": {
                    "type": "integer"
                },
                "currency_code": {
                    "type": "integer"
                },
                "lic_account": {
                    "type": "string"
                },
                "lic_code": {
                    "type": "integer"
                },
                "lic_srv_provider": {
                    "type": "integer"
                },
                "mcc": {
                    "type": "string"
                },
                "oper_dc": {
                    "type": "integer"
                }
            }
        },
        "IntegrationObject": {
            "type": "object",
            "properties": {
                "change_profile_event": {
                    "description": "Данные по изменению профиля",
                    "$ref": "#/definitions/ChangeProfileEvent"
                },
                "city_id": {
                    "description": "город",
                    "type": "integer"
                },
                "country_id": {
                    "description": "страна",
                    "type": "integer"
                },
                "dev_id": {
                    "description": "идентификатор мобильного устройства",
                    "type": "string"
                },
                "event_type": {
                    "description": "тип события",
                    "type": "integer"
                },
                "finoper": {
                    "$ref": "#/definitions/FinOper"
                },
                "ip_addr": {
                    "description": "ip-адрес клиентского устройства",
                    "type": "string"
                },
                "ip_addr_v6": {
                    "description": "ipv6-адрес клиентского устройства",
                    "type": "string"
                },
                "mac_address": {
                    "description": "физ. адрес клиентского устройства",
                    "type": "string"
                },
                "person": {
                    "description": "Данные об участнике",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Person"
                    }
                },
                "post_index": {
                    "description": "почтовый индекс",
                    "type": "string"
                },
                "reg_auth": {
                    "description": "Данные события",
                    "$ref": "#/definitions/RegAuth"
                },
                "region_id": {
                    "description": "регион/область",
                    "type": "integer"
                },
                "req_id": {
                    "description": "Технический блок - идентификатор запроса",
                    "type": "string"
                },
                "req_time_stamp": {
                    "description": "дата и время отправки формы",
                    "type": "string"
                },
                "session_id": {
                    "description": "уникальный идентификатор сессии браузера",
                    "type": "string"
                },
                "system_id": {
                    "description": "код системы",
                    "type": "integer"
                },
                "user_agent": {
                    "description": "данные из клиенткого User Agent",
                    "type": "string"
                }
            }
        },
        "Limit": {
            "type": "object",
            "properties": {
                "amount": {
                    "type": "integer"
                },
                "currency_code": {
                    "type": "integer"
                },
                "limit_type": {
                    "type": "integer"
                }
            }
        },
        "Person": {
            "type": "object",
            "properties": {
                "address": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Address"
                    }
                },
                "bs_id": {
                    "description": "идентификатор участника в системе",
                    "type": "string"
                },
                "cit_country": {
                    "description": "граждаство",
                    "type": "integer"
                },
                "client_reg_date": {
                    "description": "дата принятия клиента на обслуживание",
                    "type": "string"
                },
                "doc_number": {
                    "description": "номер документа фл",
                    "type": "string"
                },
                "doc_type": {
                    "description": "тип документа фл",
                    "type": "integer"
                },
                "fname": {
                    "description": "имя",
                    "type": "string"
                },
                "full_name": {
                    "description": "фио",
                    "type": "string"
                },
                "iin": {
                    "description": "иин/бин",
                    "type": "string"
                },
                "is_client": {
                    "description": "является ли участник клиентом организации",
                    "type": "boolean"
                },
                "lname": {
                    "description": "фамилия",
                    "type": "string"
                },
                "mname": {
                    "description": "отчество",
                    "type": "string"
                },
                "mobile_number": {
                    "description": "номер телефона",
                    "type": "string"
                },
                "opf": {
                    "description": "организационно-правовая норма",
                    "type": "integer"
                },
                "person_dc": {
                    "description": "дебет/кредит (для фин операции), справочное значение",
                    "type": "integer"
                },
                "person_role": {
                    "type": "integer"
                },
                "person_type": {
                    "type": "integer"
                },
                "reg_country": {
                    "description": "страна регистрации",
                    "type": "integer"
                },
                "reg_date": {
                    "description": "дата регистрации",
                    "type": "string"
                },
                "res_country": {
                    "description": "страна резиденства",
                    "type": "integer"
                },
                "short_name": {
                    "description": "сокращенное название юл",
                    "type": "string"
                }
            }
        },
        "Product": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "код продукта",
                    "type": "string"
                },
                "name_en": {
                    "description": "Наименование EN",
                    "type": "string"
                },
                "name_kz": {
                    "description": "Наименование KZ",
                    "type": "string"
                },
                "name_ru": {
                    "description": "Наименование RU",
                    "type": "string"
                }
            }
        },
        "RegAuth": {
            "type": "object",
            "properties": {
                "err_code": {
                    "description": "ошибка",
                    "type": "string"
                },
                "login": {
                    "description": "логин",
                    "type": "string"
                },
                "mob_number": {
                    "description": "мобильный номер",
                    "type": "string"
                }
            }
        },
        "Scenario": {
            "type": "object",
            "properties": {
                "description": {
                    "description": "Описание",
                    "type": "string"
                },
                "description_kz": {
                    "description": "Описание KZ",
                    "type": "string"
                },
                "id": {
                    "description": "id сценария",
                    "type": "integer"
                },
                "is_enable": {
                    "description": "Включен\\выключен сценарий",
                    "type": "boolean"
                },
                "name": {
                    "description": "Название",
                    "type": "string"
                }
            }
        },
        "Scoring": {
            "type": "object",
            "properties": {
                "decision_status": {
                    "description": "Решение (справочник статусов)",
                    "type": "integer"
                },
                "event_type_id": {
                    "description": "Тип события (спр.типов событий)",
                    "type": "integer"
                },
                "id": {
                    "description": "id",
                    "type": "integer"
                },
                "max_score": {
                    "description": "Максимальный балл",
                    "type": "integer"
                },
                "min_score": {
                    "description": "Минимальный балл",
                    "type": "integer"
                },
                "risk_level": {
                    "description": "Уровень риска",
                    "type": "string"
                },
                "risk_level_kz": {
                    "description": "Уровень риска KZ",
                    "type": "string"
                },
                "system_id": {
                    "description": "Канал\t(справочник каналов)",
                    "type": "integer"
                }
            }
        },
        "Spr": {
            "type": "object",
            "properties": {
                "disabled": {
                    "description": "отключено",
                    "type": "boolean"
                },
                "key": {
                    "description": "ключ",
                    "type": "string"
                },
                "name": {
                    "description": "наименование справочника",
                    "type": "string"
                },
                "value": {
                    "description": "значение",
                    "type": "string"
                }
            }
        },
        "System": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "код",
                    "type": "string"
                },
                "name_en": {
                    "description": "Наименование EN",
                    "type": "string"
                },
                "name_kz": {
                    "description": "Наименование KZ",
                    "type": "string"
                },
                "name_ru": {
                    "description": "Наименование RU",
                    "type": "string"
                }
            }
        }
    }
}`

type swaggerInfo struct {
	Version     string
	Host        string
	BasePath    string
	Schemes     []string
	Title       string
	Description string
}

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = swaggerInfo{
	Version:     "",
	Host:        "",
	BasePath:    "",
	Schemes:     []string{},
	Title:       "",
	Description: "",
}

type s struct{}

func (s *s) ReadDoc() string {
	sInfo := SwaggerInfo
	sInfo.Description = strings.Replace(sInfo.Description, "\n", "\\n", -1)

	t, err := template.New("swagger_info").Funcs(template.FuncMap{
		"marshal": func(v interface{}) string {
			a, _ := json.Marshal(v)
			return string(a)
		},
		"escape": func(v interface{}) string {
			// escape tabs
			str := strings.Replace(v.(string), "\t", "\\t", -1)
			// replace " with \", and if that results in \\", replace that with \\\"
			str = strings.Replace(str, "\"", "\\\"", -1)
			return strings.Replace(str, "\\\\\"", "\\\\\\\"", -1)
		},
	}).Parse(doc)
	if err != nil {
		return doc
	}

	var tpl bytes.Buffer
	if err := t.Execute(&tpl, sInfo); err != nil {
		return doc
	}

	return tpl.String()
}

func init() {
	swag.Register(swag.Name, &s{})
}
