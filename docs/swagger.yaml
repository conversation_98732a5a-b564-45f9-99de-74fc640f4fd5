definitions:
  Address:
    properties:
      addr_type:
        type: string
      building:
        type: string
      city:
        type: string
      city_id:
        type: integer
      district:
        type: string
      district_id:
        type: integer
      flat:
        type: string
      old_post_code:
        type: string
      post_code:
        type: string
      region:
        type: string
      region_id:
        type: integer
      rka:
        type: string
      street:
        type: string
    type: object
  Case:
    properties:
      assigned_user:
        description: сотрудник, взявшего кейс в работу
        type: integer
      case_details:
        items:
          $ref: '#/definitions/CaseDetails'
        type: array
      case_files:
        items:
          $ref: '#/definitions/CaseFiles'
        type: array
      creation_date:
        description: Дата и время создания кейса
        type: string
      decision:
        description: Id статуса обработки кейса
        type: integer
      decision_date:
        description: Дата и время принятия последнего решения ResponsibleUser
        type: string
      decision_user:
        description: Идентификатор пользователя (сотрудника), принявшего последнее
          решение по кейсу
        type: integer
      event_id:
        description: id из таблицы tb_events
        type: string
      finish_assigned_date:
        description: Дата и время, срок исполнения
        type: string
      finish_due_date:
        description: Рассчитанные дата и время, в которые должна быть завершена работа
          с кейсом - будет настраиваться в разрезе типов кейсов и приоритетов
        type: string
      id:
        type: integer
      is_archive:
        description: Флаг перемещения кейса в архив
        type: boolean
      is_confirmed:
        description: Флаг устанавливается при подтверждении кейса второй рукой (правило
          4-х глаз)
        type: boolean
      priority:
        description: Приоритет кейса в соответствии с настройками правил
        type: integer
      score:
        description: Показывает рисковый рейтинг кейса
        type: integer
      start_assigned_date:
        description: Дата и время, в которое кейс взят в работу
        type: string
      update_date:
        description: Дата и время последнего редактирования кейса
        type: string
    type: object
  CaseComment:
    properties:
      case_id:
        description: id кейса из таблицы tb_cases
        type: integer
      comment:
        description: Поле отражает последний комментарий о кейсе со стороны пользователей
        type: string
      creation_date:
        description: Дата и время создания коммента
        type: string
      id:
        type: integer
      is_active:
        description: Флаг деактивация коммента
        type: boolean
      update_date:
        description: Дата и время последнего редактирования коммента
        type: string
      user_id:
        description: Идентификатор пользователя (сотрудника)
        type: integer
    type: object
  CaseDetails:
    properties:
      case_id:
        description: id кейса из таблицы tb_cases
        type: integer
      case_type:
        description: Указывает к какому типу относится кейс
        type: integer
      scenario_desc:
        description: Дополнительное описание при необходимости
        type: string
      scenario_desc_kz:
        description: Дополнительное описание при необходимости KZ
        type: string
      scenario_id:
        description: id сценария
        type: integer
      score:
        description: Показывает рисковый рейтинг кейса
        type: integer
    type: object
  CaseExternal:
    properties:
      assigned_user:
        description: сотрудник, взявшего кейс в работу
        type: integer
      case_details:
        items:
          $ref: '#/definitions/CaseDetails'
        type: array
      case_files:
        items:
          $ref: '#/definitions/CaseFiles'
        type: array
      creation_date:
        description: Дата и время создания кейса
        type: string
      decision:
        description: Id статуса обработки кейса
        type: integer
      decision_date:
        description: Дата и время принятия последнего решения ResponsibleUser
        type: string
      decision_user:
        description: Идентификатор пользователя (сотрудника), принявшего последнее
          решение по кейсу
        type: integer
      event_id:
        description: id из таблицы tb_events
        type: string
      event_status:
        type: integer
      finish_assigned_date:
        description: Дата и время, срок исполнения
        type: string
      finish_due_date:
        description: Рассчитанные дата и время, в которые должна быть завершена работа
          с кейсом - будет настраиваться в разрезе типов кейсов и приоритетов
        type: string
      id:
        type: integer
      is_archive:
        description: Флаг перемещения кейса в архив
        type: boolean
      is_confirmed:
        description: Флаг устанавливается при подтверждении кейса второй рукой (правило
          4-х глаз)
        type: boolean
      priority:
        description: Приоритет кейса в соответствии с настройками правил
        type: integer
      score:
        description: Показывает рисковый рейтинг кейса
        type: integer
      start_assigned_date:
        description: Дата и время, в которое кейс взят в работу
        type: string
      update_date:
        description: Дата и время последнего редактирования кейса
        type: string
    type: object
  CaseFiles:
    properties:
      case_id:
        description: id кейса из таблицы tb_cases
        type: integer
      creation_date:
        description: Дата и время сохранения файла
        type: string
      file_id:
        description: id файла
        type: string
      id:
        type: integer
    type: object
  ChangeProfileEvent:
    properties:
      account_number:
        type: string
      address:
        items:
          $ref: '#/definitions/Address'
        type: array
      card_exp_date:
        type: string
      card_id:
        type: integer
      card_name:
        type: string
      card_number:
        type: string
      card_type:
        type: string
      limit:
        items:
          $ref: '#/definitions/Limit'
        type: array
      password_changed:
        type: boolean
      password_changed_req:
        type: boolean
      pin_changed:
        type: boolean
      pin_reset:
        type: boolean
      push:
        type: boolean
      push_crd:
        type: boolean
      reg_date:
        type: string
      sms_crd:
        type: boolean
      three_ds:
        type: boolean
    type: object
  Event:
    properties:
      err_code:
        type: integer
      err_info:
        type: string
      event:
        $ref: '#/definitions/IntegrationObject'
      status:
        type: integer
      status_info:
        type: string
    type: object
  EventType:
    properties:
      event_type:
        description: Наименование
        type: string
      event_type_kz:
        description: Наименование
        type: string
      id:
        description: код
        type: integer
    type: object
  FinOper:
    properties:
      branch:
        type: string
      creation_date_time:
        type: string
      finoper_dc:
        items:
          $ref: '#/definitions/FinoperDc'
        type: array
      kbk_code:
        type: string
      kno_code:
        type: string
      knp_code:
        type: string
      oper_date_time:
        type: string
      oper_id:
        type: string
      oper_sts:
        type: integer
      oper_sub_type:
        type: integer
      oper_type:
        type: integer
      paypost_id:
        type: integer
      person:
        items:
          $ref: '#/definitions/Person'
        type: array
      post_id:
        type: integer
      product_grp_id:
        type: integer
      product_id:
        type: string
      reason:
        type: string
      ref_oper_id:
        type: string
      service_id:
        type: string
    type: object
  FinService:
    properties:
      code:
        description: код продукта
        type: string
      description:
        description: Описание
        type: string
    type: object
  FinoperDc:
    properties:
      account_number:
        type: string
      amount:
        type: number
      amount_kzt:
        type: number
      bank_bic:
        type: string
      bank_name:
        type: string
      bs_account:
        type: string
      card_exp_date:
        type: string
      card_id:
        type: integer
      card_name:
        type: string
      card_number:
        type: string
      card_open_date:
        type: string
      card_type:
        type: integer
      currency_code:
        type: integer
      lic_account:
        type: string
      lic_code:
        type: integer
      lic_srv_provider:
        type: integer
      mcc:
        type: string
      oper_dc:
        type: integer
    type: object
  IntegrationObject:
    properties:
      change_profile_event:
        $ref: '#/definitions/ChangeProfileEvent'
        description: Данные по изменению профиля
      city_id:
        description: город
        type: integer
      country_id:
        description: страна
        type: integer
      dev_id:
        description: идентификатор мобильного устройства
        type: string
      event_type:
        description: тип события
        type: integer
      finoper:
        $ref: '#/definitions/FinOper'
      ip_addr:
        description: ip-адрес клиентского устройства
        type: string
      ip_addr_v6:
        description: ipv6-адрес клиентского устройства
        type: string
      mac_address:
        description: физ. адрес клиентского устройства
        type: string
      person:
        description: Данные об участнике
        items:
          $ref: '#/definitions/Person'
        type: array
      post_index:
        description: почтовый индекс
        type: string
      reg_auth:
        $ref: '#/definitions/RegAuth'
        description: Данные события
      region_id:
        description: регион/область
        type: integer
      req_id:
        description: Технический блок - идентификатор запроса
        type: string
      req_time_stamp:
        description: дата и время отправки формы
        type: string
      session_id:
        description: уникальный идентификатор сессии браузера
        type: string
      system_id:
        description: код системы
        type: integer
      user_agent:
        description: данные из клиенткого User Agent
        type: string
    type: object
  Limit:
    properties:
      amount:
        type: integer
      currency_code:
        type: integer
      limit_type:
        type: integer
    type: object
  Person:
    properties:
      address:
        items:
          $ref: '#/definitions/Address'
        type: array
      bs_id:
        description: идентификатор участника в системе
        type: string
      cit_country:
        description: граждаство
        type: integer
      client_reg_date:
        description: дата принятия клиента на обслуживание
        type: string
      doc_number:
        description: номер документа фл
        type: string
      doc_type:
        description: тип документа фл
        type: integer
      fname:
        description: имя
        type: string
      full_name:
        description: фио
        type: string
      iin:
        description: иин/бин
        type: string
      is_client:
        description: является ли участник клиентом организации
        type: boolean
      lname:
        description: фамилия
        type: string
      mname:
        description: отчество
        type: string
      mobile_number:
        description: номер телефона
        type: string
      opf:
        description: организационно-правовая норма
        type: integer
      person_dc:
        description: дебет/кредит (для фин операции), справочное значение
        type: integer
      person_role:
        type: integer
      person_type:
        type: integer
      reg_country:
        description: страна регистрации
        type: integer
      reg_date:
        description: дата регистрации
        type: string
      res_country:
        description: страна резиденства
        type: integer
      short_name:
        description: сокращенное название юл
        type: string
    type: object
  Product:
    properties:
      code:
        description: код продукта
        type: string
      name_en:
        description: Наименование EN
        type: string
      name_kz:
        description: Наименование KZ
        type: string
      name_ru:
        description: Наименование RU
        type: string
    type: object
  RegAuth:
    properties:
      err_code:
        description: ошибка
        type: string
      login:
        description: логин
        type: string
      mob_number:
        description: мобильный номер
        type: string
    type: object
  Scenario:
    properties:
      description:
        description: Описание
        type: string
      description_kz:
        description: Описание KZ
        type: string
      id:
        description: id сценария
        type: integer
      is_enable:
        description: Включен\выключен сценарий
        type: boolean
      name:
        description: Название
        type: string
    type: object
  Scoring:
    properties:
      decision_status:
        description: Решение (справочник статусов)
        type: integer
      event_type_id:
        description: Тип события (спр.типов событий)
        type: integer
      id:
        description: id
        type: integer
      max_score:
        description: Максимальный балл
        type: integer
      min_score:
        description: Минимальный балл
        type: integer
      risk_level:
        description: Уровень риска
        type: string
      risk_level_kz:
        description: Уровень риска KZ
        type: string
      system_id:
        description: "Канал\t(справочник каналов)"
        type: integer
    type: object
  Spr:
    properties:
      disabled:
        description: отключено
        type: boolean
      key:
        description: ключ
        type: string
      name:
        description: наименование справочника
        type: string
      value:
        description: значение
        type: string
    type: object
  System:
    properties:
      id:
        description: код
        type: string
      name_en:
        description: Наименование EN
        type: string
      name_kz:
        description: Наименование KZ
        type: string
      name_ru:
        description: Наименование RU
        type: string
    type: object
info:
  contact: {}
paths:
  /case:
    put:
      parameters:
      - description: Add request
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/Case'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Case'
      summary: Изменение кейса
      tags:
      - cases
  /case/assign:
    patch:
      parameters:
      - description: Add request
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/Case'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Case'
      summary: Переназначить кейс
      tags:
      - cases
  /case/comment:
    post:
      parameters:
      - description: Add request
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/CaseComment'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/CaseComment'
      summary: Добавление коммента к кейсу
      tags:
      - cases
    put:
      parameters:
      - description: Add request
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/CaseComment'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/CaseComment'
      summary: Изменение коммента к кейсу
      tags:
      - cases
  /case/comment/{id}:
    delete:
      parameters:
      - description: comment id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/CaseComment'
      summary: Добавление коммента к кейсу
      tags:
      - cases
    get:
      parameters:
      - description: case id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/CaseComment'
      summary: Получение списка комментов кейсов
      tags:
      - cases
  /case/list:
    get:
      parameters:
      - description: page
        in: query
        name: page
        type: string
      - description: per_page
        in: query
        name: per_page
        type: string
      - description: Сортировка asc/desc
        enum:
        - asc
        - desc
        in: query
        name: sort
        type: string
      - description: Поля для сортировки через запятую
        in: query
        name: sort_field
        type: string
      - description: Значение поля для фильтрации, через запятую (в паре с filter_field)
        in: query
        name: filter_values
        type: string
      - description: Название полей для фильтрации, через запятую (в паре с filter_value)
        in: query
        name: filter_fields
        type: string
      - description: Тип фильтра полное совпадение (=) или совпадение по like
        enum:
        - =
        - like
        in: query
        name: filter_type
        type: string
      - description: Пересечение или дополнение
        enum:
        - and
        - or
        in: query
        name: filter_logic
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/CaseExternal'
      summary: Получение списка кейсов
      tags:
      - cases
  /event/case/{case}:
    get:
      parameters:
      - description: case id
        in: path
        name: case
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Event'
      summary: Получение евента по id case
      tags:
      - events
  /event/list:
    get:
      parameters:
      - description: page
        in: query
        name: page
        type: string
      - description: per_page
        in: query
        name: per_page
        type: string
      - description: Сортировка asc/desc
        enum:
        - asc
        - desc
        in: query
        name: sort
        type: string
      - description: Поля для сортировки через запятую
        in: query
        name: sort_field
        type: string
      - description: Значение поля для фильтрации, через запятую (в паре с filter_field)
        in: query
        name: filter_values
        type: string
      - description: Название полей для фильтрации, через запятую (в паре с filter_value)
        in: query
        name: filter_fields
        type: string
      - description: Тип фильтра полное совпадение (=) или совпадение по like
        enum:
        - =
        - like
        in: query
        name: filter_type
        type: string
      - description: Пересечение или дополнение
        enum:
        - and
        - or
        in: query
        name: filter_logic
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Event'
      summary: Получение списка евентов
      tags:
      - events
  /hdbk/case_buttons:
    get:
      parameters:
      - description: page
        in: query
        name: page
        type: string
      - description: per_page
        in: query
        name: per_page
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Spr'
      summary: Получение кнопок обработки кейса
      tags:
      - hdbk
    put:
      parameters:
      - description: Add request
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/Spr'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Spr'
      summary: Изменение кнопок обработки кейса
      tags:
      - hdbk
  /hdbk/event_statuses:
    get:
      parameters:
      - description: page
        in: query
        name: page
        type: string
      - description: per_page
        in: query
        name: per_page
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Spr'
      summary: Получение справочника статусов
      tags:
      - hdbk
  /hdbk/event_types/list:
    get:
      parameters:
      - description: page
        in: query
        name: page
        type: string
      - description: per_page
        in: query
        name: per_page
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/EventType'
      summary: Получение справочника типов событий
      tags:
      - hdbk
  /hdbk/fin-services:
    post:
      parameters:
      - description: Add request
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/FinService'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/FinService'
      summary: Добавление значения в справочнике фин сервисов
      tags:
      - hdbk
    put:
      parameters:
      - description: Add request
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/FinService'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/FinService'
      summary: Изменение значения в справочнике фин сервисов
      tags:
      - hdbk
  /hdbk/fin-services/{code}:
    delete:
      parameters:
      - description: code
        in: path
        name: code
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/FinService'
      summary: Удаление продукта из справочника по code
      tags:
      - hdbk
  /hdbk/fin-services/list:
    get:
      parameters:
      - description: page
        in: query
        name: page
        type: string
      - description: per_page
        in: query
        name: per_page
        type: string
      - description: Сортировка asc/desc
        enum:
        - asc
        - desc
        in: query
        name: sort
        type: string
      - description: Поля для сортировки через запятую
        in: query
        name: sort_field
        type: string
      - description: Значение поля для фильтрации, через запятую (в паре с filter_field)
        in: query
        name: filter_values
        type: string
      - description: Название полей для фильтрации, через запятую (в паре с filter_value)
        in: query
        name: filter_fields
        type: string
      - description: Тип фильтра полное совпадение (=) или совпадение по like
        enum:
        - =
        - like
        in: query
        name: filter_type
        type: string
      - description: Пересечение или дополнение
        enum:
        - and
        - or
        in: query
        name: filter_logic
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/FinService'
      summary: Получение справочника фин сервисов
      tags:
      - hdbk
  /hdbk/history:
    put:
      parameters:
      - description: Add request
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/Spr'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Spr'
      summary: Изменение настроек хранения данных
      tags:
      - hdbk
  /hdbk/history/list:
    get:
      parameters:
      - description: page
        in: query
        name: page
        type: string
      - description: per_page
        in: query
        name: per_page
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Spr'
      summary: Получение настроек хранения данных
      tags:
      - hdbk
  /hdbk/product:
    post:
      parameters:
      - description: Add request
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/Product'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Product'
      summary: Добавление значения в справочнике продуктов
      tags:
      - hdbk
    put:
      parameters:
      - description: Add request
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/Product'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Product'
      summary: Изменение значения в справочнике продуктов
      tags:
      - hdbk
  /hdbk/product/{code}:
    delete:
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Product'
      summary: Удаление продукта из справочника по code
      tags:
      - hdbk
  /hdbk/product/list:
    get:
      parameters:
      - description: page
        in: query
        name: page
        type: string
      - description: per_page
        in: query
        name: per_page
        type: string
      - description: Сортировка asc/desc
        enum:
        - asc
        - desc
        in: query
        name: sort
        type: string
      - description: Поля для сортировки через запятую
        in: query
        name: sort_field
        type: string
      - description: Значение поля для фильтрации, через запятую (в паре с filter_field)
        in: query
        name: filter_values
        type: string
      - description: Название полей для фильтрации, через запятую (в паре с filter_value)
        in: query
        name: filter_fields
        type: string
      - description: Тип фильтра полное совпадение (=) или совпадение по like
        enum:
        - =
        - like
        in: query
        name: filter_type
        type: string
      - description: Пересечение или дополнение
        enum:
        - and
        - or
        in: query
        name: filter_logic
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Product'
      summary: Получение справочника продуктов
      tags:
      - hdbk
  /hdbk/solution:
    put:
      parameters:
      - description: Add request
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/Spr'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Spr'
      summary: Изменение настроек по песочнице
      tags:
      - hdbk
  /hdbk/solution/list:
    get:
      parameters:
      - description: page
        in: query
        name: page
        type: string
      - description: per_page
        in: query
        name: per_page
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Spr'
      summary: Получение настроек по песочнице
      tags:
      - hdbk
  /hdbk/system:
    post:
      parameters:
      - description: Add request
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/System'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/System'
      summary: Добавление значения в справочнике систем
      tags:
      - hdbk
    put:
      parameters:
      - description: Add request
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/FinService'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/System'
      summary: Изменение значения в справочнике систем
      tags:
      - hdbk
  /hdbk/system/{id}:
    delete:
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/System'
      summary: Удаление значения из справочника по id
      tags:
      - hdbk
  /hdbk/system/list:
    get:
      parameters:
      - description: page
        in: query
        name: page
        type: string
      - description: per_page
        in: query
        name: per_page
        type: string
      - description: Сортировка asc/desc
        enum:
        - asc
        - desc
        in: query
        name: sort
        type: string
      - description: Поля для сортировки через запятую
        in: query
        name: sort_field
        type: string
      - description: Значение поля для фильтрации, через запятую (в паре с filter_field)
        in: query
        name: filter_values
        type: string
      - description: Название полей для фильтрации, через запятую (в паре с filter_value)
        in: query
        name: filter_fields
        type: string
      - description: Тип фильтра полное совпадение (=) или совпадение по like
        enum:
        - =
        - like
        in: query
        name: filter_type
        type: string
      - description: Пересечение или дополнение
        enum:
        - and
        - or
        in: query
        name: filter_logic
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/System'
      summary: Получение справочника систем
      tags:
      - hdbk
  /scenario:
    put:
      parameters:
      - description: Add request
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/Scenario'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Scenario'
      summary: Изменение сценария
      tags:
      - scenarios
  /scenario/list:
    get:
      parameters:
      - description: page
        in: query
        name: page
        type: string
      - description: per_page
        in: query
        name: per_page
        type: string
      - description: Сортировка asc/desc
        enum:
        - asc
        - desc
        in: query
        name: sort
        type: string
      - description: Поля для сортировки через запятую
        in: query
        name: sort_field
        type: string
      - description: Значение поля для фильтрации, через запятую (в паре с filter_field)
        in: query
        name: filter_values
        type: string
      - description: Название полей для фильтрации, через запятую (в паре с filter_value)
        in: query
        name: filter_fields
        type: string
      - description: Тип фильтра полное совпадение (=) или совпадение по like
        enum:
        - =
        - like
        in: query
        name: filter_type
        type: string
      - description: Пересечение или дополнение
        enum:
        - and
        - or
        in: query
        name: filter_logic
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Scenario'
      summary: Получение списка сценариев
      tags:
      - scenarios
  /scoring:
    post:
      parameters:
      - description: Add request
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/Scoring'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Scoring'
      summary: Добавление значения парметров скоринга
      tags:
      - scoring
    put:
      parameters:
      - description: Add request
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/Scoring'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Scoring'
      summary: Изменение параметров скоринга
      tags:
      - scoring
  /scoring/{id}:
    delete:
      parameters:
      - description: code
        in: path
        name: code
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Scoring'
      summary: Удаление параметров скоринга
      tags:
      - scoring
  /scoring/list:
    get:
      parameters:
      - description: page
        in: query
        name: page
        type: string
      - description: per_page
        in: query
        name: per_page
        type: string
      - description: Сортировка asc/desc
        enum:
        - asc
        - desc
        in: query
        name: sort
        type: string
      - description: Поля для сортировки через запятую
        in: query
        name: sort_field
        type: string
      - description: Значение поля для фильтрации, через запятую (в паре с filter_field)
        in: query
        name: filter_values
        type: string
      - description: Название полей для фильтрации, через запятую (в паре с filter_value)
        in: query
        name: filter_fields
        type: string
      - description: Тип фильтра полное совпадение (=) или совпадение по like
        enum:
        - =
        - like
        in: query
        name: filter_type
        type: string
      - description: Пересечение или дополнение
        enum:
        - and
        - or
        in: query
        name: filter_logic
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/Scoring'
      summary: Получение параметров скоринга
      tags:
      - scoring
swagger: "2.0"
