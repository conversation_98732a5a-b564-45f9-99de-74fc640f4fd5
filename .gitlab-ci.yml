variables:
  GIT_DEPTH: 1
  GROUP_NAME: antifraud-online
  GROUP_NAME_PROD: antifraud-online-prod
  PROJECT_VCS_REF: $CI_COMMIT_SHORT_SHA
  PROJECT_BRANCH: $CI_COMMIT_REF_NAME
  NEXUS_URL: nexus.post.kz:8200
  K8S_NAMESPACE: antifraud-online
  K8S_NAMESPACE_PROD: antifraud-online-prod

stages:
  - build
  - build_prod
  - nexus
  - nexus_prod
  - deploy
  - deploy_prod

build-image:
  stage: build
  only:
    - dev
  script:
    - echo "==== Project Metadata ===="
    - UNIX_TIME=$(date '+%s')
    - TIME_RFC=$(date -u --date=@"${UNIX_TIME}" '+%Y-%m-%dT%H:%M:%SZ')
    - TIME_UTC=$(date -u --date=@"${UNIX_TIME}" '+%Y%m%d-%H%M%S')
    - BUILD_ID="${TIME_UTC}--${PROJECT_BRANCH}--${PROJECT_VCS_REF}"
    - PROJECT_NAME=$CI_PROJECT_NAME
    - IMAGE_FULL_NAME="${NEXUS_URL}/${GROUP_NAME}/${PROJECT_NAME}"
    - echo "Build ID:\ ${BUILD_ID}"
    - echo "Image ID:\ ${IMAGE_FULL_NAME}"
    - echo "Time rfc:\ ${TIME_RFC}"
    - echo "Name:\     ${PROJECT_NAME}"
    - echo "==== Building the image ===="
    - docker build -t $IMAGE_FULL_NAME:$BUILD_ID -f Dockerfile .
    - echo "IMAGE_FULL_NAME=${IMAGE_FULL_NAME}" >> build.env
    - echo "BUILD_ID=${BUILD_ID}" >> build.env
  artifacts:
    reports:
      dotenv: build.env

build-image-prod:
  stage: build_prod
  only:
    - master 
  script:
    - echo "==== Project Metadata ===="
    - UNIX_TIME=$(date '+%s')
    - TIME_RFC=$(date -u --date=@"${UNIX_TIME}" '+%Y-%m-%dT%H:%M:%SZ')
    - TIME_UTC=$(date -u --date=@"${UNIX_TIME}" '+%Y%m%d-%H%M%S')
    - BUILD_ID="${TIME_UTC}--${PROJECT_BRANCH}--${PROJECT_VCS_REF}"
    - PROJECT_NAME=$CI_PROJECT_NAME
    - IMAGE_FULL_NAME="${NEXUS_URL}/${GROUP_NAME_PROD}/${PROJECT_NAME}"
    - echo "Build ID:\ ${BUILD_ID}"
    - echo "Image ID:\ ${IMAGE_FULL_NAME}"
    - echo "Time rfc:\ ${TIME_RFC}"
    - echo "Name:\     ${PROJECT_NAME}"
    - echo "==== Building the image ===="
    - docker build -t $IMAGE_FULL_NAME:$BUILD_ID -f Dockerfile .
    - echo "IMAGE_FULL_NAME=${IMAGE_FULL_NAME}" >> build.env
    - echo "BUILD_ID=${BUILD_ID}" >> build.env
  artifacts:
    reports:
      dotenv: build.env

## Nexus jobs
push-to-nexus:
  stage: nexus
  variables:
    GIT_STRATEGY: none
    GIT_CHECKOUT: 'false'
  script:
    - PROJECT_NAME=$CI_PROJECT_NAME
    - IMAGE_FULL_NAME="${NEXUS_URL}/${GROUP_NAME}/${PROJECT_NAME}"
    - echo "Tagging the image as latest of $PROJECT_BRANCH"
    - docker image tag "$IMAGE_FULL_NAME:$BUILD_ID" "$IMAGE_FULL_NAME:latest-$PROJECT_BRANCH"
    - echo "==== Pushing to nexus ===="
    - docker push $IMAGE_FULL_NAME:$BUILD_ID
    - docker push $IMAGE_FULL_NAME:latest-$PROJECT_BRANCH
    - docker rmi $IMAGE_FULL_NAME:$BUILD_ID
    - docker rmi $IMAGE_FULL_NAME:latest-$PROJECT_BRANCH
  only:
    - dev

## deploy jobs
deploy:
  variables:
    GIT_STRATEGY: none
    GIT_CHECKOUT: 'false'
  stage: deploy
  script:
    - envsubst < ./kube/template-dev.yaml > ./kube/manifest.yaml
    - echo $KUBE_CONFIG | base64 -d > ./kube/kube.config
    - kubectl --kubeconfig ./kube/kube.config apply -f ./kube/manifest.yaml
    - kubectl --kubeconfig ./kube/kube.config get pods -n $K8S_NAMESPACE
  only:
    - dev

## Nexus jobs
push-to-nexus_prod:
  stage: nexus_prod
  variables:
    GIT_STRATEGY: none
    GIT_CHECKOUT: 'false'
  script:
    - PROJECT_NAME=$CI_PROJECT_NAME
    - IMAGE_FULL_NAME="${NEXUS_URL}/${GROUP_NAME_PROD}/${PROJECT_NAME}"
    - echo "Tagging the image as latest of $PROJECT_BRANCH"
    - docker image tag "$IMAGE_FULL_NAME:$BUILD_ID" "$IMAGE_FULL_NAME:latest-$PROJECT_BRANCH"
    - echo "==== Pushing to nexus ===="
    - docker push $IMAGE_FULL_NAME:$BUILD_ID
    - docker push $IMAGE_FULL_NAME:latest-$PROJECT_BRANCH
    - docker rmi $IMAGE_FULL_NAME:$BUILD_ID
    - docker rmi $IMAGE_FULL_NAME:latest-$PROJECT_BRANCH
  only:
    - master

## deploy jobs
deploy_prod:
  when: manual
  variables:
    GIT_STRATEGY: none
    GIT_CHECKOUT: 'false'
  stage: deploy_prod
  script:
    - envsubst < ./kube/template-prod.yaml > ./kube/manifest.yaml
    - echo $KUBE_CONFIG_PROD | base64 -d > ./kube/kube.config
    - kubectl --kubeconfig ./kube/kube.config apply -f ./kube/manifest.yaml
    - kubectl --kubeconfig ./kube/kube.config get pods -n $K8S_NAMESPACE_PROD
  only:
    - master
