package services

import (
	"admin-panel/db"
	"strconv"
	"strings"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

var INTERVAL = []string{"year", "month", "day", "hour", "minute"}
var INTERVAL2 = []string{"years", "months", "days", "hours", "minutes"}

func IsValidUUID(u string) bool {
	_, err := uuid.Parse(u)
	return err == nil
}

func GetCountFromDB(tableName string) (cnt int64, err error) {
	err = db.DBConn.Table(tableName).Count(&cnt).Error
	return
}

func GetListFromDB(tx func(tx *gorm.DB) *gorm.DB, database *gorm.DB, tableName string, resultStruct interface{}) error {
	if database == nil {
		return db.DBConn.Table(tableName).Scopes(tx).Find(resultStruct).Error
	}
	return database.Table(tableName).Scopes(tx).Find(resultStruct).Error
}

func containsStr(s []string, str string) bool {
	for _, v := range s {
		if v == str {
			return true
		}
	}
	return false
}

func ValidPeriod(period string) bool {
	period = strings.TrimSpace(strings.ToLower(period))

	arr := strings.Split(period, " ")

	if len(arr) != 2 {
		return false
	}

	i, err := strconv.Atoi(arr[0])
	if err != nil {
		return false
	}

	if i < 1 {
		return false
	}

	if i == 1 && containsStr(INTERVAL, arr[1]) == false {
		return false
	}

	if i > 1 && containsStr(INTERVAL2, arr[1]) == false {
		return false
	}

	return true
}
