package services

import (
	"admin-panel/db"
	"admin-panel/models"
	"strings"
)

/*-------------Spr------------*/
func GetSpr(sprName string) (cnt int64, list []models.Spr, err error) {
	var tblName string = models.Spr{}.TableName()
	sprName = strings.TrimSpace(strings.ToLower(sprName))

	err = db.DBConn.Table(tblName).Where("name = ?", sprName).Count(&cnt).Error
	if err != nil {
		return
	}

	err = db.DBConn.Table(tblName).Where("name = ?", sprName).Find(&list).Error

	return
}
