package services

import (
	"admin-panel/models"
	"encoding/json"
	"fmt"
	"log"

	tool "projects.post.kz/antifraud-online/tool"
)

func SendResponse(event Event) (err error) {

	url := fmt.Sprintf("%v/%v%v", models.Env.ServiceUrl, models.Config.SolutionResponse, "/api/v1/send")
	jsonByte, _ := json.Marshal(event)

	_, _, err = tool.HttpReqJSON("POST", url, jsonByte, nil, nil, nil, nil)
	if err != nil {
		log.Println("send response err: ", err)
	}
	return
}
