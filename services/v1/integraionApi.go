package v1

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"os/signal"
	"producer/models"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	elastic_tools "projects.post.kz/antifraud-online/elastic-tools"
)

func IntegrationApi(c *fiber.Ctx) (err error) {
	var (
		obj elastic_tools.IntegrationObject
	)
	if err = c.BodyParser(&obj); err != nil {
		return
	}

	if err = obj.Validation(models.GetDB()); err != nil {
		return
	}

	tools := elastic_tools.NewElasticTool(models.GetEs())

	if obj.TransactionId, err = uuid.NewUUID(); err != nil {
		return
	}
	obj.ReqTimeStamp = time.Now()

	if err = tools.CreateTransactionRecord(&obj); err != nil {
		fmt.Println("err", err)
		return
	}

	jsonObj, err := json.Marshal(&obj)
	if err != nil {
		return
	}

	signals := make(chan os.Signal, 1)
	signal.Notify(signals, os.Interrupt)
	models.ProduceMessage(signals, string(jsonObj))
	log.Println("transaction Id: ", obj.TransactionId)

	err = tools.SetInQueue(obj.TransactionId)

	_ = models.Respond("success", c)
	return
}
