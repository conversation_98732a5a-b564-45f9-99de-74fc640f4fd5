package services

import (
	"admin-panel/db"
	"admin-panel/models"
	"time"

	"gorm.io/gorm"
	tool "projects.post.kz/antifraud-online/tool"
)

type Case tool.Case //@name Case

func (Case) TableName() string {
	return tool.Case{}.TableName()
}

type CaseComment tool.CaseComment //@name CaseComment

func (CaseComment) TableName() string {
	return tool.CaseComment{}.TableName()
}

type CasesResult models.CasesResult

type CaseExternal struct {
	Case
	EventStatus int `json:"event_status"`
} //@name CaseExternal

func filterFiles(ss []tool.CaseFiles, filter func(tool.CaseFiles) bool) (ret []tool.CaseFiles) {
	for _, s := range ss {
		if filter(s) {
			ret = append(ret, s)
		}
	}
	return
}

func SaveCaseFiles(tx *gorm.DB, cs Case) (newFiles []tool.CaseFiles, err error) {
	var existsFiles []tool.CaseFiles

	err = tx.Where("case_id = ?", cs.Id).Find(&existsFiles).Error

	if err != nil {
		return
	}

	//Удаляем файл если он не был передан с фронта
	for _, f := range existsFiles {

		fe := filterFiles(cs.CaseFiles, func(cf tool.CaseFiles) bool { return cf.FileId == f.FileId })

		if len(fe) == 0 {
			err = tx.Delete(&f).Error
			if err != nil {
				return
			}
		}
	}

	//Сохраняем файл если его нет в БД
	for _, f := range cs.CaseFiles {
		f.CaseId = cs.Id

		fe := filterFiles(existsFiles, func(cf tool.CaseFiles) bool { return cf.FileId == f.FileId })

		if len(fe) == 0 {
			f.CreationDate = time.Now()

			err = tx.Save(&f).Error
			if err != nil {
				return
			}
		} else {
			f.Id = fe[0].Id
		}

		newFiles = append(newFiles, f)
	}

	return
}

func GetCaseById(caseId int) (cs Case, err error) {
	err = db.DBConn.Preload("CaseDetails").Preload("CaseFiles").Where("id = ?", caseId).Find(&cs).Error
	return
}

func GetCaseComments(caseId int) (comments []CaseComment, err error) {
	err = db.DBConn.Where("case_id = ? and is_active = true", caseId).Find(&comments).Error
	return
}

func GetCaseComment(commentId int) (comment CaseComment, err error) {
	err = db.DBConn.Where("id = ?", commentId).Find(&comment).Error
	return
}
