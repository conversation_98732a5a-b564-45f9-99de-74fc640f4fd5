package services

import (
	"admin-panel/db"
	"bytes"
	"encoding/xml"
	"fmt"
	"io/ioutil"
	"net/http"

	elk "projects.post.kz/antifraud-online/elastic-tools"
)

type Event elk.EventObject //@name Event

type SoapEventData struct {
	Status     int
	CardNumber string
	FullName   string
}

type SetCardStatusResponse struct {
	XMLName xml.Name `xml:"Envelope"`
	Body    struct {
		Response struct {
			Result struct {
				RetCode string `xml:"RetCode"`
				RetMsg  string `xml:"RetMsg"`
			} `xml:"SetCardStatusResult"`
		} `xml:"Body>SetCardStatusResponse"`
	} `xml:"Body"`
}

func (Event) TableName() string {
	return elk.EventObject{}.TableName()
}

func GetEventById(id string) (event Event, err error) {
	err = db.DBConn.Preload("ChangeProfileEvent").
		Preload("FinOper").
		Preload("FinOper.FinoperDc").
		Preload("FinOper.Person").
		Where("req_id = ?", id).First(&event).Error
	return
}

func GetCardNumber(caseId int) (SoapEventData, error) {
	var result SoapEventData

	query := `
        SELECT 
            e.status, 
            fodc.card_number,
    		p.full_name
        FROM 
            public.tb_events AS e
            LEFT JOIN public.tb_Cases AS cpe ON cpe.event_id = e.req_id
            LEFT JOIN tb_event_fin_operation AS fo ON fo.event_id = e.req_id
            LEFT JOIN tb_event_fin_oper_details AS fodc ON fodc.operation_id = fo.id
            LEFT JOIN tb_event_person AS p ON p.operation_id = fo.id
        WHERE 
            cpe.id = ? AND fodc.oper_dc = 1 AND e.status = 3
        LIMIT 1;
    `

	err := db.DBConn.Raw(query, caseId).Scan(&result).Error
	return result, err
}

func updateEventStatus(id string, status int) error {
	return db.DBConn.Model(Event{}).Where("req_id = ?", id).Update("status", status).Error
}

func GetEventByCaseId(caseId int) (event Event, err error) {
	err = db.DBConn.Joins("INNER JOIN tb_cases ON tb_cases.event_id = tb_events.req_id").Preload("ChangeProfileEvent").Preload("Person").Preload("FinOper").Preload("FinOper.FinoperDc").Preload("FinOper.Person").Where("tb_cases.id = ?", caseId).First(&event).Error
	return
}

func UpdateEvent(cs Case) error {

	//Если != 0 и подтвержден, то значит кейс обработали
	if cs.Decision == 0 || cs.IsConfirmed == false {
		return nil
	}

	event, err := GetEventById(cs.EventId.String())

	if err != nil {
		return err
	}

	event.Status = cs.Decision

	err = updateEventStatus(cs.EventId.String(), cs.Decision)
	if err != nil {
		return err
	}

	if cs.Decision == 3 {
		soapData, err := GetCardNumber(cs.Id)
		if err != nil {
			return err
		}

		err = SendToWay4(soapData)
		if err != nil {
			return err
		}
	}

	go SendResponse(event)

	return nil
}

func SendToWay4(data SoapEventData) error {
	fmt.Printf("Sending to SOAP service: Status=%d, CardNumber=%s\n", data.Status, data.CardNumber)
	if data.Status != 3 {
		return fmt.Errorf("status is not 3, skipping SOAP request")
	}

	soapBody := fmt.Sprintf(`
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:wsin="http://www.openwaygroup.com/wsint">
    <soapenv:Header>
        <wsin:UserInfo>officer="wx_admin"</wsin:UserInfo>
        <wsin:CorrelationId>223</wsin:CorrelationId>
    </soapenv:Header>
    <soapenv:Body>
        <wsin:SetCardStatus>
            <wsin:ContractSearchMethod>CONTRACT_NUMBER</wsin:ContractSearchMethod>
            <wsin:ContractIdentifier>%s</wsin:ContractIdentifier>
            <wsin:NewStatus>14</wsin:NewStatus>
            <wsin:Reason>Номер заявки в workflow 329562550</wsin:Reason>
            <wsin:UserInfo>officer="wx_admin"</wsin:UserInfo>
        </wsin:SetCardStatus>
    </soapenv:Body>
</soapenv:Envelope>`, data.CardNumber)

	req, err := http.NewRequest("POST", "http://************:17000/wsruntime_default/ws/", bytes.NewBuffer([]byte(soapBody)))
	if err != nil {
		return err
	}

	req.Header.Add("Content-Type", "text/xml;charset=UTF-8")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	var soapResp SetCardStatusResponse
	err = xml.Unmarshal(respBody, &soapResp)
	if err != nil {
		return err
	}

	fmt.Printf("SOAP Response: RetCode=%s, RetMsg=%s\n",
		soapResp.Body.Response.Result.RetCode,
		soapResp.Body.Response.Result.RetMsg,
	)

	if soapResp.Body.Response.Result.RetCode != "0" {
		return fmt.Errorf("SOAP error: %s", soapResp.Body.Response.Result.RetMsg)
	}

	return nil

}
