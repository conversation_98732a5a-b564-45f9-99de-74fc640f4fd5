package db

import (
	"admin-panel/models"
	"database/sql"
	"fmt"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DBConn *gorm.DB

func InitDatabase() (sqlDB *sql.DB) {
	env := models.Env

	dbUri := fmt.Sprintf("host=%s port=%s user=%s dbname=%s sslmode=disable password=%s", env.DbHost, env.DbPort, env.DbUser, env.DbName, env.DbPass)
	var err error
	DBConn, err = gorm.Open(postgres.Open(dbUri), &gorm.Config{
		CreateBatchSize: 1000,
		Logger:          logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		panic(err)
	}
	//DBConn = DBConn.Debug()
	sqlDB, err = DBConn.DB()
	if err != nil {
		panic(err)
	}

	sqlDB.SetConnMaxLifetime(time.Duration(1) * time.Hour)
	sqlDB.SetMaxIdleConns(0)
	sqlDB.SetMaxOpenConns(15)

	fmt.Println("Database connection successfully opened")
	return
}
