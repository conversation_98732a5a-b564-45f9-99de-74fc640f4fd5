package db

import (
	"admin-panel/models"
	"context"
	"fmt"
	"strconv"

	"github.com/go-redis/redis/v8"
	tool "projects.post.kz/antifraud-online/tool"
)

var (
	RedisCtx    = context.Background()
	RedisClient *redis.Client
)

func RedisNewClient() {
	var err error
	env := models.Env
	db, _ := strconv.Atoi(env.RedisDb)
	RedisClient, err = tool.RedisNewClient(env.RedisHost, env.RedisPass, db, RedisCtx)

	if err != nil {
		panic(err)
	}

	fmt.Println("Redis connection successfully opened")
}
