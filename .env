GOPRIVATE = "1"
TZ = "Asia/Almaty"

# одно из следующих значений: local, dev, prod
contour = "prod"
#contour = "local"

#connection_db_string_local = "host=************* port=5432 user=post password=HbskR683nc #dbname=postgres sslmode=disable application_name=authorization-local"
#connection_db_string_dev = "host=************* port=5432 user=post password=HbskR683nc #dbname=postgres sslmode=disable application_name=authorization"
connection_db_string_prod = "host=************* port=5432 user=post password=mFCrtt8lAPjUANH6WViR dbname=antifraud sslmode=disable application_name=authorization"

#c#onnection_kafka_string_local = "*************:9093"
#connection_kafka_string_dev = "*************:9093"
connection_kafka_string_prod = "*************:9093"

#connection_elastic_string_local = "http://*************:9200"
#connection_elastic_string_dev = "http://*************:9200"
connection_elastic_string_prod = "http://*************:9200"

db_user="post"
db_pass="mFCrtt8lAPjUANH6WViR"
db_name="antifraud"
db_host="*************"
db_port="5432"
kafka_url="*************:9093"
kafka_topic="ProdTopic"
kafka_consumer_group="40"

elastic_url="http://*************:9200"
elastic_user="elastic"
elastic_pass="kkXVjTPbwyNjm7Fr"

port = ":8080"

