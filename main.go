package main

import (
	"fmt"
	"log"
	"producer/models"
	"producer/services"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/recover"
)

func main() {
	log.Println("Start loading environments")
	models.Env.Load()
	env := models.Env

	log.Println("Env loaded > ", env)
	log.Println("Start connecting to DB")

	sqlDB, err := models.NewDB(&env)
	if err != nil {
		panic(err)
	}
	log.Println("DB connected > ", sqlDB)
	log.Println("Start connecting to Kafka")

	asyncProducer, err := models.NewKafka(&env)
	if err != nil {
		panic(err)
	}

	log.Println("Kafka connected > ", asyncProducer)
	log.Println("Start connecting to Elastic")
	_, err = models.NewElastic(&env)
	if err != nil {
		panic(err)
	}

	log.Println("Elastic connected > ")

	defer func() {
		fmt.Print("Too bad")
		_ = sqlDB.Close()
		_ = asyncProducer.Close()
	}()

	log.Println("Start conguring Fiber")
	recoverCfg := fiber.Config{
		ErrorHandler: func(ctx *fiber.Ctx, err error) error {
			code := fiber.StatusInternalServerError
			if e, ok := err.(*fiber.Error); ok {
				code = e.Code
			}
			_ = ctx.Status(code).JSON(fiber.Map{
				"status":  false,
				"message": err.Error(),
			})
			return nil
		},
	}

	app := fiber.New(recoverCfg)
	app.Use(recover.New())
	app.Use(cors.New(), recover.New())
	services.Routes(app)

	err = app.Listen(env.Port)
	if err != nil {
		panic(err)
	}
}
