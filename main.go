package main

import (
	v1 "admin-panel/api/v1"
	"admin-panel/db"
	"admin-panel/docs"
	"admin-panel/middleware"
	"admin-panel/models"

	"github.com/gin-gonic/gin"

	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

func main() {
	models.Config.Load()
	models.Env.Load()
	db.RedisNewClient()

	sqlDB := db.InitDatabase()
	defer func() {
		_ = sqlDB.Close()
	}()

	r := gin.Default()
	r.Use(middleware.CORSMiddleware())
	v1.Routes(r)

	//docs.SwaggerInfo.BasePath = "/api/v1/"
	docs.SwaggerInfo.BasePath = "/admin-panel/api/v1/"
	swagger := r.Group("swagger")
	swagger.GET("*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	r.<PERSON>(models.Env.Port)
}
