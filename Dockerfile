FROM golang:1.21-alpine as builder
RUN apk update && apk add --no-cache git
WORKDIR /app
ADD ./ /app
RUN export GO111MODULE=on && export GOPRIVATE=projects.post.kz && go get projects.post.kz/antifraud-online/elastic-tools &&  go get projects.post.kz/antifraud-online/tool
RUN export GOOS=linux 
RUN export CGO_ENABLED=0
RUN  go get gorm.io/driver/postgres
RUN go install
RUN go build -o index
ENV TZ=Asia/Oral

FROM alpine:latest
RUN apk --no-cache add tzdata
WORKDIR /app
COPY --from=builder /app/index /app/index
COPY --from=builder /app/swagger /app/swagger
#COPY ./config.json /app/config.json
COPY ./.env /app/.env

ENV TZ=Asia/Oral
EXPOSE 8080
ENTRYPOINT ["/app/index"]
