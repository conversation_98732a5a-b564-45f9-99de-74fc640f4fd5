FROM golang:1.21-alpine as builder
RUN apk update && apk add --no-cache git
WORKDIR /app
ADD ./ /app
RUN go mod tidy
RUN go install github.com/swaggo/swag/cmd/swag@v1.16.3
# RUN swag init  # включи при необходимости
RUN export GOOS=linux && export CGO_ENABLED=0 && go build -o index

FROM scratch
COPY --from=builder /app/index /app/index
COPY ./config.json /app/config.json
ENV TZ=Asia/Almaty
WORKDIR /app
EXPOSE 8080
ENTRYPOINT ["/app/index"]
