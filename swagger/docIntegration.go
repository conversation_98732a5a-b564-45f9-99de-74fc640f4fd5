package swagger

// swagger:operation POST /producer/api/v1/integration Integration post_integration
// ---
// summary: Отправка данных на интеграционный серивис Fraud Prevention
// produces:
//  - application/json
// parameters:
//  - name: body
//    in: body
//    description: Интеграционная сущность
//    schema:
//     $ref: "#/definitions/integration_object"
// responses:
//   '200':
//     description: Возвращает success
//     schema:
//       $ref: "#/definitions/http_response"
