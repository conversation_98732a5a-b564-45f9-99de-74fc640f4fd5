{"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "Сервис", "title": "Package classification  Сервис", "contact": {"name": "... ...", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "http://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "basePath": "/", "paths": {"/producer/api/v1/integration": {"post": {"produces": ["application/json"], "tags": ["Integration"], "summary": "Отправка данных на интеграционный серив<PERSON><PERSON>", "operationId": "post_integration", "parameters": [{"description": "Интеграционная сущность", "name": "body", "in": "body", "schema": {"$ref": "#/definitions/integration_object"}}], "responses": {"200": {"description": "Возвращает success", "schema": {"$ref": "#/definitions/http_response"}}}}}}, "definitions": {"Address": {"type": "object", "properties": {"addr_type": {"type": "string", "x-go-name": "AddrType"}, "building": {"type": "string", "x-go-name": "Building"}, "city": {"type": "string", "x-go-name": "City"}, "city_id": {"type": "integer", "format": "int64", "x-go-name": "CityId"}, "district": {"type": "string", "x-go-name": "District"}, "district_id": {"type": "integer", "format": "int64", "x-go-name": "DistrictId"}, "flat": {"type": "string", "x-go-name": "Flat"}, "old_post_code": {"type": "string", "x-go-name": "OldPostCode"}, "post_code": {"type": "string", "x-go-name": "PostCode"}, "region": {"type": "string", "x-go-name": "Region"}, "region_id": {"type": "integer", "format": "int64", "x-go-name": "RegionId"}, "rka": {"type": "string", "x-go-name": "RKA"}, "street": {"type": "string", "x-go-name": "Street"}}, "x-go-package": "gitlab01.kazpost.kz/kazpost/elastic-tools"}, "ChangeProfileEvent": {"type": "object", "properties": {"account_number": {"type": "string", "x-go-name": "AccountNumber"}, "address": {"type": "array", "items": {"$ref": "#/definitions/Address"}, "x-go-name": "Address"}, "card_exp_date": {"type": "string", "x-go-name": "CardExpDate"}, "card_id": {"type": "integer", "format": "int64", "x-go-name": "CardId"}, "card_name": {"type": "string", "x-go-name": "CardName"}, "card_number": {"type": "string", "x-go-name": "CardNumber"}, "card_type": {"type": "string", "x-go-name": "CardType"}, "limit": {"type": "array", "items": {"$ref": "#/definitions/Limit"}, "x-go-name": "Limit"}, "password_changed": {"type": "boolean", "x-go-name": "PasswordChanged"}, "password_changed_req": {"type": "boolean", "x-go-name": "PasswordChangedReq"}, "pin_changed": {"type": "boolean", "x-go-name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "pin_reset": {"type": "boolean", "x-go-name": "<PERSON>n<PERSON><PERSON><PERSON>"}, "push": {"type": "boolean", "x-go-name": "<PERSON><PERSON>"}, "push_crd": {"type": "boolean", "x-go-name": "PushCrd"}, "reg_date": {"type": "string", "format": "date-time", "x-go-name": "RegDate"}, "sms_crd": {"type": "boolean", "x-go-name": "SmsCrd"}, "three_ds": {"type": "boolean", "x-go-name": "ThreeDS"}}, "x-go-package": "gitlab01.kazpost.kz/kazpost/elastic-tools"}, "FinOper": {"type": "object", "properties": {"branch": {"type": "string", "x-go-name": "Branch"}, "creation_date_time": {"type": "string", "format": "date-time", "x-go-name": "CreationDateTime"}, "finoper_dc": {"type": "array", "items": {"$ref": "#/definitions/FinoperDc"}, "x-go-name": "FinoperDc"}, "kbk_code": {"type": "string", "x-go-name": "KbkCode"}, "kno_code": {"type": "string", "x-go-name": "KnoCode"}, "knp_code": {"type": "string", "x-go-name": "KnpCode"}, "oper_date_time": {"type": "string", "format": "date-time", "x-go-name": "OperDateTime"}, "oper_id": {"type": "string", "x-go-name": "OperId"}, "oper_sts": {"type": "integer", "format": "int64", "x-go-name": "OperSts"}, "post_id": {"type": "integer", "format": "int64", "x-go-name": "PostId"}, "paypost_id": {"type": "integer", "format": "int64", "x-go-name": "PaypostId"}, "oper_sub_type": {"type": "integer", "format": "int64", "x-go-name": "OperSubType"}, "oper_type": {"type": "integer", "format": "int64", "x-go-name": "OperType"}, "person": {"type": "array", "items": {"$ref": "#/definitions/Person"}, "x-go-name": "Person"}, "product_grp_id": {"type": "integer", "format": "int64", "x-go-name": "ProductGrpId"}, "product_id": {"type": "string", "x-go-name": "ProductId"}, "reason": {"type": "string", "x-go-name": "Reason"}, "ref_oper_id": {"type": "string", "x-go-name": "RefOperId"}, "service_id": {"type": "integer", "format": "int64", "x-go-name": "ServiceId"}}, "x-go-package": "producer/models"}, "FinoperDc": {"type": "object", "properties": {"account_number": {"type": "string", "x-go-name": "AccountNumber"}, "amount": {"type": "integer", "format": "int64", "x-go-name": "Amount"}, "amount_kzt": {"type": "integer", "format": "int64", "x-go-name": "AmountKzt"}, "bank_bic": {"type": "string", "x-go-name": "BankBic"}, "bank_name": {"type": "string", "x-go-name": "BankName"}, "bs_account": {"type": "string", "x-go-name": "BsAccount"}, "card_exp_date": {"type": "string", "format": "date-time", "x-go-name": "CardExpDate"}, "card_name": {"type": "string", "x-go-name": "CardName"}, "card_number": {"type": "string", "x-go-name": "CardNumber"}, "card_open_date": {"type": "string", "format": "date-time", "x-go-name": "CardOpenDate"}, "card_type": {"type": "integer", "format": "int64", "x-go-name": "CardType"}, "currency_code": {"type": "integer", "format": "int64", "x-go-name": "CurrencyCode"}, "lic_account": {"type": "string", "x-go-name": "LicA<PERSON>unt"}, "lic_code": {"type": "integer", "format": "int64", "x-go-name": "LicCode"}, "lic_srv_provider": {"type": "integer", "format": "int64", "x-go-name": "LicSrvProvider"}, "mcc": {"type": "string", "x-go-name": "Mcc"}, "oper_dc": {"type": "integer", "format": "int64", "x-go-name": "OperDc"}}, "x-go-package": "producer/models"}, "Limit": {"type": "object", "properties": {"amount": {"type": "integer", "format": "int64", "x-go-name": "Amount"}, "currency_code": {"type": "integer", "format": "int64", "x-go-name": "CurrencyCode"}, "limit_type": {"type": "integer", "format": "int64", "x-go-name": "LimitType"}}, "x-go-package": "gitlab01.kazpost.kz/kazpost/elastic-tools"}, "Person": {"type": "object", "properties": {"address": {"type": "array", "items": {"$ref": "#/definitions/Address"}, "x-go-name": "Address"}, "bs_id": {"type": "string", "x-go-name": "BsId"}, "cit_country": {"type": "integer", "format": "int64", "x-go-name": "CitCountry"}, "client_reg_date": {"type": "string", "format": "date-time", "x-go-name": "ClientRegDate"}, "doc_number": {"type": "string", "x-go-name": "DocNumber"}, "doc_type": {"type": "integer", "format": "int64", "x-go-name": "DocType"}, "fname": {"type": "string", "x-go-name": "FName"}, "full_name": {"type": "string", "x-go-name": "FullName"}, "iin": {"type": "string", "x-go-name": "IIN"}, "is_client": {"type": "boolean", "x-go-name": "IsClient"}, "lname": {"type": "string", "x-go-name": "LName"}, "mname": {"type": "string", "x-go-name": "MName"}, "mobile_number": {"type": "string", "x-go-name": "MobileNumber"}, "opf": {"type": "integer", "format": "int64", "x-go-name": "OPF"}, "person_dc": {"type": "integer", "format": "int64", "x-go-name": "PersonDc"}, "person_role": {"type": "integer", "format": "int64", "x-go-name": "PersonRole"}, "person_type": {"type": "integer", "format": "int64", "x-go-name": "PersonType"}, "reg_country": {"type": "integer", "format": "int64", "x-go-name": "RegCountry"}, "reg_date": {"type": "string", "format": "date-time", "x-go-name": "RegDate"}, "res_country": {"type": "integer", "format": "int64", "x-go-name": "ResCountry"}, "short_name": {"type": "string", "x-go-name": "ShortName"}}, "x-go-package": "producer/models"}, "RegAuth": {"type": "object", "properties": {"err_code": {"type": "string", "x-go-name": "ErrCode"}, "login": {"type": "string", "x-go-name": "<PERSON><PERSON>"}, "mob_number": {"type": "string", "x-go-name": "MobNumber"}}, "x-go-package": "gitlab01.kazpost.kz/kazpost/elastic-tools"}, "UUID": {"description": "A UUID is a 128 bit (16 byte) Universal Unique IDentifier as defined in RFC\n4122.", "type": "array", "items": {"type": "integer", "format": "uint8"}, "x-go-package": "github.com/google/uuid"}, "http_response": {"type": "object", "properties": {"message": {"type": "string", "x-go-name": "Message"}, "result": {"type": "object", "x-go-name": "Result"}, "status": {"type": "boolean", "x-go-name": "Status"}}, "x-go-name": "Response", "x-go-package": "producer/models"}, "integration_object": {"type": "object", "properties": {"change_profile_event": {"$ref": "#/definitions/ChangeProfileEvent"}, "city_id": {"type": "integer", "format": "int64", "x-go-name": "CityId"}, "country_id": {"type": "integer", "format": "int64", "x-go-name": "CountryId"}, "dev_id": {"type": "string", "x-go-name": "DevId"}, "event_type": {"type": "integer", "format": "int64", "x-go-name": "EventType"}, "finoper": {"$ref": "#/definitions/FinOper"}, "ip_addr": {"type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ip_addr_v6": {"type": "string", "x-go-name": "IpAddressV6"}, "mac_address": {"type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "person": {"description": "Данные об участнике", "type": "array", "items": {"$ref": "#/definitions/Person"}, "x-go-name": "Person"}, "post_index": {"type": "string", "x-go-name": "PostIndex"}, "reg_auth": {"$ref": "#/definitions/RegAuth"}, "region_id": {"type": "integer", "format": "int64", "x-go-name": "RegionId"}, "req_id": {"description": "Технический блок - идентификатор запроса", "type": "string", "x-go-name": "ReqId", "$ref": "#/definitions/UUID"}, "req_time_stamp": {"type": "string", "format": "date-time", "x-go-name": "ReqTimeStamp"}, "session_id": {"type": "string", "x-go-name": "SessionId"}, "system_id": {"type": "integer", "format": "int64", "x-go-name": "SystemId"}, "user_agent": {"type": "string", "x-go-name": "UserAgent"}}, "x-go-name": "IntegrationObject", "x-go-package": "gitlab01.kazpost.kz/kazpost/elastic-tools"}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "security": [{"api_key": []}]}